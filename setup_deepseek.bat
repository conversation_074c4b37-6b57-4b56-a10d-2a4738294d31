@echo off
echo ========================================
echo 中医智慧诊疗系统 - DeepSeek API 配置
echo ========================================

echo.
echo 第一步：检查Python环境...

REM 尝试多种Python命令
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    goto :python_found
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    goto :python_found
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    goto :python_found
)

echo ❌ Python未找到或未添加到PATH环境变量
echo.
echo 您提到已安装Python 3.10，请尝试以下解决方案：
echo.
echo 方案1：重新安装Python
echo 1. 访问 https://python.org 下载Python 3.10
echo 2. 安装时勾选 "Add Python to PATH"
echo 3. 重启命令行后重新运行此脚本
echo.
echo 方案2：手动添加PATH
echo 1. 找到Python安装目录（通常在 C:\Python310\ 或 C:\Users\<USER>\AppData\Local\Programs\Python\Python310\）
echo 2. 将Python目录添加到系统PATH环境变量
echo 3. 重启命令行后重新运行此脚本
echo.
echo 方案3：使用Python启动器
echo 1. 在命令行中尝试运行：py --version
echo 2. 如果成功，请重新运行此脚本
echo.
pause
exit /b 1

:python_found

echo ✅ Python环境检查通过
%PYTHON_CMD% --version

echo.
echo 第二步：检查DeepSeek API配置...
if not exist "backend\.env" (
    echo 📝 创建API配置文件...
    copy "backend\.env.example" "backend\.env"
    echo.
    echo ⚠️  请按以下步骤配置DeepSeek API：
    echo.
    echo 1. 访问 https://platform.deepseek.com/ 注册账号
    echo 2. 获取API密钥（格式：sk-xxxxxxxxxxxxxxxx）
    echo 3. 编辑 backend\.env 文件
    echo 4. 将 your_deepseek_api_key_here 替换为您的实际API密钥
    echo.
    echo 配置完成后重新运行此脚本
    echo.
    pause
    start notepad backend\.env
    exit /b 1
)

echo ✅ 配置文件存在，检查API密钥...
findstr /C:"your_deepseek_api_key_here" backend\.env >nul
if %errorlevel% equ 0 (
    echo ❌ 请先配置您的DeepSeek API密钥
    echo.
    echo 编辑 backend\.env 文件，将 your_deepseek_api_key_here 替换为您的实际API密钥
    echo.
    pause
    start notepad backend\.env
    exit /b 1
)

echo ✅ API密钥已配置

echo.
echo 第三步：安装Python依赖...
cd backend
echo 正在安装依赖包...
%PYTHON_CMD% -m pip install flask flask-cors requests python-dotenv openai
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试使用国内镜像...
    %PYTHON_CMD% -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors requests python-dotenv openai
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo ✅ 依赖安装完成

echo.
echo 第四步：启动后端服务...
echo.
echo ========================================
echo 🚀 启动中医智慧诊疗系统 - 完整版
echo ========================================
echo.
echo 📍 后端服务: http://localhost:5000
echo 📍 前端页面: 请打开 frontend\index.html
echo.
echo 💡 完整版特点:
echo    - 集成DeepSeek AI，智能诊断
echo    - 支持所有症状类型
echo    - 个性化诊疗建议
echo    - 脉象舌象四诊合参
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

%PYTHON_CMD% app.py
