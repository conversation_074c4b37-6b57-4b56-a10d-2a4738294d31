# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset ms DAYS_OF_WEEK_ABBREV [list \
        "Aha"\
        "Isn"\
        "Se<PERSON>"\
        "Rab"\
        "<PERSON>ha"\
        "Ju<PERSON>"\
        "Sab"]
    ::msgcat::mcset ms DAYS_OF_WEEK_FULL [list \
        "Ahad"\
        "Isnin"\
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "Ju<PERSON><PERSON>"\
        "Sabtu"]
    ::msgcat::mcset ms MONTHS_ABBREV [list \
        "Jan"\
        "Feb"\
        "Mac"\
        "Apr"\
        "Mei"\
        "Jun"\
        "Jul"\
        "Ogos"\
        "Sep"\
        "Okt"\
        "Nov"\
        "Dis"\
        ""]
    ::msgcat::mcset ms MONTHS_FULL [list \
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "Mac"\
        "April"\
        "Mei"\
        "Jun"\
        "<PERSON><PERSON>"\
        "<PERSON>gos"\
        "September"\
        "Okto<PERSON>"\
        "November"\
        "Disember"\
        ""]
}
