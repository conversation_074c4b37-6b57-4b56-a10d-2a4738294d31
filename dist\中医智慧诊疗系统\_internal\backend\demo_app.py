"""
中医智慧诊疗系统 - 演示版本
不依赖外部API，使用内置知识库
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
from tcm_demo_service import TCMDemoService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 初始化中医诊疗演示服务
tcm_service = TCMDemoService()
logger.info("中医诊疗演示服务初始化成功")

@app.route('/', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "message": "中医智慧诊疗系统演示版运行正常",
        "version": "demo-1.0",
        "service_available": True
    })

@app.route('/api/diagnosis', methods=['POST'])
def get_diagnosis():
    """获取中医诊断建议"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'symptoms' not in data:
            return jsonify({
                "error": "请提供症状信息",
                "success": False
            }), 400
        
        symptoms = data['symptoms'].strip()
        if not symptoms:
            return jsonify({
                "error": "症状信息不能为空",
                "success": False
            }), 400
        
        logger.info(f"收到诊断请求，症状: {symptoms[:50]}...")
        
        # 调用中医诊疗服务
        result = tcm_service.get_tcm_diagnosis(symptoms)
        
        if result.get('success'):
            logger.info("诊断完成")
        else:
            logger.error(f"诊断失败: {result.get('error')}")
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"处理诊断请求时发生错误: {e}")
        return jsonify({
            "error": f"服务器内部错误: {str(e)}",
            "success": False
        }), 500

@app.route('/api/common-symptoms', methods=['GET'])
def get_common_symptoms():
    """获取常见症状列表"""
    common_symptoms = [
        "头痛、头晕",
        "咳嗽、咳痰", 
        "胃痛、腹胀",
        "失眠、多梦",
        "腰膝酸软",
        "月经不调",
        "便秘、腹泻",
        "心悸、胸闷",
        "疲劳乏力",
        "食欲不振",
        "手脚冰凉",
        "口干舌燥",
        "情绪焦虑",
        "关节疼痛",
        "皮肤瘙痒"
    ]
    
    return jsonify({
        "success": True,
        "symptoms": common_symptoms
    })

@app.route('/api/demo-info', methods=['GET'])
def get_demo_info():
    """获取演示版本信息"""
    return jsonify({
        "success": True,
        "info": {
            "version": "演示版 1.0",
            "description": "本演示版使用内置中医知识库，支持头痛、咳嗽、胃痛等常见症状的诊断",
            "supported_symptoms": ["头痛", "咳嗽", "胃痛"],
            "features": [
                "中医诊断",
                "病机分析", 
                "方剂推荐",
                "君臣佐使分析",
                "针灸穴位建议",
                "注意事项"
            ],
            "note": "完整版本将集成DeepSeek API，支持更多症状和更智能的诊断"
        }
    })

if __name__ == '__main__':
    print("=" * 50)
    print("🏥 中医智慧诊疗系统 - 演示版")
    print("=" * 50)
    print("✅ 演示版特点：")
    print("   - 无需API密钥，即开即用")
    print("   - 内置中医知识库")
    print("   - 支持头痛、咳嗽、胃痛等常见症状")
    print()
    print("🌐 访问地址：")
    print("   - 后端API: http://localhost:5000")
    print("   - 前端页面: 请打开 frontend/index.html")
    print()
    print("🔧 完整版本：")
    print("   - 配置DeepSeek API密钥后运行 app.py")
    print("   - 支持更多症状和智能诊断")
    print()
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
