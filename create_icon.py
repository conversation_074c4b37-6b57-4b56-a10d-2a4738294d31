"""
创建简单的应用图标
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    # 创建一个64x64的图标
    size = 64
    img = Image.new('RGBA', (size, size), (44, 62, 80, 255))  # 深蓝色背景
    draw = ImageDraw.Draw(img)
    
    # 绘制一个简单的医疗十字
    cross_color = (255, 255, 255, 255)  # 白色
    cross_width = 8
    
    # 垂直线
    draw.rectangle([size//2 - cross_width//2, 10, size//2 + cross_width//2, size-10], fill=cross_color)
    # 水平线
    draw.rectangle([10, size//2 - cross_width//2, size-10, size//2 + cross_width//2], fill=cross_color)
    
    # 保存为ICO格式
    img.save('icon.ico', format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
    print("✅ 图标文件 icon.ico 创建成功")
    
except ImportError:
    print("⚠️ PIL库未安装，跳过图标创建")
    print("如需图标，请运行: pip install Pillow")
except Exception as e:
    print(f"⚠️ 图标创建失败: {e}")
