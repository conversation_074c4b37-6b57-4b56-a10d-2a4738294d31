# 🚀 DeepSeek API 快速启动指南

## 📋 5分钟快速配置

### 1️⃣ 获取DeepSeek API密钥（2分钟）

1. **访问** https://platform.deepseek.com/
2. **注册账号** 并完成邮箱验证
3. **进入控制台** → 找到"API密钥"页面
4. **创建新密钥** → 复制API密钥（格式：sk-xxxxxxxxxxxxxxxx）

### 2️⃣ 配置API密钥（1分钟）

1. **复制配置文件**：
   - 找到 `backend/.env.example` 文件
   - 复制并重命名为 `backend/.env`

2. **编辑配置**：
   - 打开 `backend/.env` 文件
   - 将 `your_deepseek_api_key_here` 替换为您的实际API密钥

   ```
   DEEPSEEK_API_KEY=sk-your-actual-api-key-here
   DEEPSEEK_BASE_URL=https://api.deepseek.com
   ```

### 3️⃣ 安装Python环境（如需要）

**Windows用户**：
- 下载：https://python.org
- 安装时勾选"Add Python to PATH"

**验证安装**：
```bash
python --version
```

### 4️⃣ 启动系统（2分钟）

**方法一：自动化脚本**
```bash
双击运行 setup_deepseek.bat
```

**方法二：手动启动**
```bash
# 进入后端目录
cd backend

# 安装依赖（首次运行）
pip install flask flask-cors requests python-dotenv openai

# 启动服务
python app.py
```

### 5️⃣ 访问系统

1. **后端启动成功**后，会显示：
   ```
   * Running on http://127.0.0.1:5000
   ```

2. **打开前端**：
   - 浏览器打开 `frontend/index.html`
   - 系统会自动连接到后端API

## ✅ 验证配置成功

### 测试API连接
访问：http://localhost:5000/
应该看到：
```json
{
  "status": "healthy",
  "message": "中医智慧诊疗系统后端服务运行正常",
  "service_available": true
}
```

### 测试完整功能
1. **输入复杂症状**：
   ```
   患者男性，35岁，头痛反复发作2月余，痛势较剧，
   痛如针刺，固定不移，夜间尤甚，面色晦暗，
   舌质紫暗有瘀斑，脉象涩
   ```

2. **选择脉象舌象**：
   - 脉象：涩脉
   - 舌质：紫暗、有瘀斑

3. **获取AI诊断**：
   - 点击"获取中医诊断建议"
   - 查看DeepSeek AI提供的专业分析

## 🆚 完整版优势

与离线版相比，完整版支持：

- ✅ **任意症状**：不限于头痛、咳嗽、胃痛
- ✅ **复杂病情**：多系统、多症状综合分析
- ✅ **个性化诊断**：基于具体症状的精准分析
- ✅ **AI智能**：DeepSeek大模型的专业医学知识
- ✅ **动态调整**：根据脉象舌象调整诊断方案

## 🔧 常见问题

### Q: API密钥在哪里获取？
A: 访问 https://platform.deepseek.com/ 注册账号后，在控制台的"API密钥"页面创建。

### Q: 提示"API调用失败"怎么办？
A: 检查：
1. API密钥是否正确
2. 网络连接是否正常
3. DeepSeek账号是否有余额

### Q: Python环境问题？
A: 确保：
1. Python版本 3.8+
2. 已添加到PATH环境变量
3. 能正常运行 `python --version`

### Q: 依赖安装失败？
A: 尝试：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors requests python-dotenv openai
```

## 💡 使用技巧

### 获得最佳诊断效果
1. **详细描述症状**：包括发病时间、症状特点、伴随症状
2. **准确选择脉象舌象**：这会显著提高诊断准确性
3. **提供病史信息**：既往病史、家族史等
4. **描述体质特点**：寒热喜恶、饮食偏好等

### 示例输入
```
患者女性，28岁，月经不调3个月，经期延后7-10天，
经量偏少，色淡质稀，小腹隐痛，腰膝酸软，
畏寒肢冷，夜尿频多，舌质淡胖有齿痕，苔薄白，脉沉细
```

## 🎉 开始使用

配置完成后，您就可以体验AI驱动的专业中医诊疗系统了！

**享受智能中医诊断的魅力吧！** 🏥🤖✨
