# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset kw DAYS_OF_WEEK_ABBREV [list \
        "Sul"\
        "Lun"\
        "Mth"\
        "Mhr"\
        "Yow"\
        "Gwe"\
        "Sad"]
    ::msgcat::mcset kw DAYS_OF_WEEK_FULL [list \
        "De Sul"\
        "<PERSON> Lu<PERSON>"\
        "<PERSON>"\
        "<PERSON> Me<PERSON>"\
        "De <PERSON><PERSON>"\
        "<PERSON>"\
        "<PERSON> Sadorn"]
    ::msgcat::mcset kw MONTHS_ABBREV [list \
        "Gen"\
        "Whe"\
        "Mer"\
        "Ebr"\
        "Me"\
        "Evn"\
        "Gor"\
        "Est"\
        "Gwn"\
        "Hed"\
        "Du"\
        "Kev"\
        ""]
    ::msgcat::mcset kw MONTHS_FULL [list \
        "Mys Genver"\
        "Mys Whevrel"\
        "My<PERSON> Merth"\
        "<PERSON><PERSON> E<PERSON>l"\
        "My<PERSON> Me"\
        "<PERSON><PERSON> <PERSON>"\
        "Mys Gortheren"\
        "Mye Est"\
        "Mys Gwyngala"\
        "Mys Hedra"\
        "Mys Du"\
        "Mys Ke<PERSON>hu"\
        ""]
}
