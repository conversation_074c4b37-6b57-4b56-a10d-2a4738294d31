import os
import requests
import time
from typing import Dict, Any
from dotenv import load_dotenv

load_dotenv()

class TCMDiagnosisService:
    def __init__(self):
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')

        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY not found in environment variables")

    def get_tcm_diagnosis(self, symptoms: str) -> Dict[str, Any]:
        """
        调用DeepSeek API获取中医诊断建议
        """
        prompt = self._create_tcm_prompt(symptoms)

        try:
            response = self._call_deepseek_api(prompt)
            return self._parse_response(response)
        except Exception as e:
            return {
                "error": f"API调用失败: {str(e)}",
                "success": False
            }

    def _create_tcm_prompt(self, symptoms: str) -> str:
        """
        创建中医诊疗的专业提示词
        """
        return f"""
你是一位经验丰富的中医专家，请根据患者提供的信息进行中医诊断分析。

患者信息：{symptoms}

请按照以下格式提供详细的中医诊疗建议：

## 1. 中医诊断
[基于中医理论的诊断结果]

## 2. 疾病机理
[从中医角度分析疾病的发生机理，包括病因、病机、病位等]

## 3. 治疗方案
[总体治疗原则和方法]

## 4. 推荐方剂
[具体的中药方剂名称和组成]

## 5. 中药处方分析
### 君药：[主要药物及作用]
### 臣药：[辅助药物及作用]
### 佐药：[调和药物及作用]
### 使药：[引经药物及作用]

## 6. 针灸治疗
### 主要经络：[相关经络]
### 推荐穴位：[具体穴位及定位]
### 针灸方法：[针刺手法和注意事项]

## 7. 注意事项
### 饮食调理：[饮食建议]
### 生活起居：[生活方式建议]
### 禁忌事项：[需要避免的事项]
### 复诊建议：[何时复诊]

请确保所有建议都基于传统中医理论，并提醒患者在实际治疗前咨询专业中医师。
"""

    def _call_deepseek_api(self, prompt: str) -> str:
        """
        调用DeepSeek API，带重试机制
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.7,
            'max_tokens': 2000
        }

        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f'{self.base_url}/v1/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=60  # 60秒超时
                )

                if response.status_code != 200:
                    raise Exception(f"API请求失败: {response.status_code} - {response.text}")

                result = response.json()
                return result['choices'][0]['message']['content']

            except requests.exceptions.Timeout:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5  # 递增等待时间
                    time.sleep(wait_time)
                    continue
                else:
                    raise Exception("API调用超时，请检查网络连接或稍后重试")

            except requests.exceptions.ConnectionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3
                    time.sleep(wait_time)
                    continue
                else:
                    raise Exception("网络连接失败，请检查网络设置")

            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e

    def _parse_response(self, response: str) -> Dict[str, Any]:
        """
        解析API响应并结构化返回
        """
        return {
            "success": True,
            "diagnosis": response,
            "timestamp": self._get_current_time()
        }

    def _get_current_time(self) -> str:
        """
        获取当前时间
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
