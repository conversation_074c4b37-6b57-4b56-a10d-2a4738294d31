from flask import Flask, request, jsonify
from flask_cors import CORS
from tcm_service import TCMDiagnosisService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 初始化中医诊疗服务
try:
    tcm_service = TCMDiagnosisService()
    logger.info("中医诊疗服务初始化成功")
except Exception as e:
    logger.error(f"中医诊疗服务初始化失败: {e}")
    tcm_service = None

@app.route('/', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "message": "中医智慧诊疗系统后端服务运行正常",
        "service_available": tcm_service is not None
    })

@app.route('/api/diagnosis', methods=['POST'])
def get_diagnosis():
    """获取中医诊断建议"""
    try:
        # 检查服务是否可用
        if not tcm_service:
            return jsonify({
                "error": "中医诊疗服务不可用，请检查API配置",
                "success": False
            }), 500
        
        # 获取请求数据
        data = request.get_json()
        if not data or 'symptoms' not in data:
            return jsonify({
                "error": "请提供症状信息",
                "success": False
            }), 400
        
        symptoms = data['symptoms'].strip()
        if not symptoms:
            return jsonify({
                "error": "症状信息不能为空",
                "success": False
            }), 400
        
        logger.info(f"收到诊断请求，症状: {symptoms[:50]}...")
        
        # 调用中医诊疗服务
        result = tcm_service.get_tcm_diagnosis(symptoms)
        
        if result.get('success'):
            logger.info("诊断完成")
        else:
            logger.error(f"诊断失败: {result.get('error')}")
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"处理诊断请求时发生错误: {e}")
        return jsonify({
            "error": f"服务器内部错误: {str(e)}",
            "success": False
        }), 500

@app.route('/api/common-symptoms', methods=['GET'])
def get_common_symptoms():
    """获取常见症状列表"""
    common_symptoms = [
        "头痛、头晕",
        "咳嗽、咳痰",
        "胃痛、腹胀",
        "失眠、多梦",
        "腰膝酸软",
        "月经不调",
        "便秘、腹泻",
        "心悸、胸闷",
        "疲劳乏力",
        "食欲不振",
        "手脚冰凉",
        "口干舌燥",
        "情绪焦虑",
        "关节疼痛",
        "皮肤瘙痒"
    ]
    
    return jsonify({
        "success": True,
        "symptoms": common_symptoms
    })

if __name__ == '__main__':
    print("启动中医智慧诊疗系统后端服务...")
    print("请确保已设置DEEPSEEK_API_KEY环境变量")
    app.run(debug=True, host='0.0.0.0', port=5000)
