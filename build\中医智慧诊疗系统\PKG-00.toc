('F:\\win py\\win py\\build\\中医智慧诊疗系统\\中医智慧诊疗系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'F:\\win py\\win py\\build\\中医智慧诊疗系统\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'F:\\win py\\win py\\build\\中医智慧诊疗系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'F:\\win py\\win py\\build\\中医智慧诊疗系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'F:\\win py\\win py\\build\\中医智慧诊疗系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'F:\\win py\\win py\\build\\中医智慧诊疗系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'F:\\win py\\win py\\build\\中医智慧诊疗系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('tcm_launcher', 'F:\\win py\\win py\\tcm_launcher.py', 'PYSOURCE')],
 'python312.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
