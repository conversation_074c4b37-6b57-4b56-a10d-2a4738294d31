@echo off
chcp 65001 >nul
echo ========================================
echo 中医智慧诊疗系统 - 一键启动
echo ========================================

echo.
echo 正在启动演示版后端服务...
cd backend
start /B python demo_app.py

echo.
echo 等待服务启动...
timeout /t 3 >nul

echo.
echo 正在打开前端界面...
cd ..
start "" "frontend\index.html"

echo.
echo ========================================
echo 🎉 启动完成！
echo ========================================
echo.
echo 📍 后端服务: http://localhost:5000
echo 📍 前端界面: 已在浏览器中打开
echo.
echo 💡 使用说明:
echo 1. 填写患者信息（性别、年龄）
echo 2. 输入症状描述
echo 3. 选择脉象和舌象
echo 4. 点击"获取中医诊断建议"
echo.
echo 按任意键关闭此窗口...
pause >nul
