# Python环境安装指南

## 当前状态

✅ **好消息**：系统已经支持离线模式！
- 前端界面可以直接使用，无需后端服务
- 支持头痛、咳嗽、胃痛等症状的模拟诊断
- 提供完整的中医诊疗建议

## 离线模式使用

1. **直接打开**：浏览器打开 `frontend/index.html`
2. **输入症状**：如"头痛三天"、"咳嗽有痰"、"胃痛胃胀"
3. **获取诊断**：系统会自动使用内置知识库提供诊断

## 如需完整功能（可选）

如果您想使用完整版本（集成DeepSeek API），需要安装Python：

### Windows系统

1. **下载Python**
   - 访问：https://www.python.org/downloads/
   - 下载最新版本的Python 3.8+

2. **安装Python**
   - 运行下载的安装程序
   - ⚠️ **重要**：勾选"Add Python to PATH"
   - 选择"Install Now"

3. **验证安装**
   ```cmd
   python --version
   ```

4. **启动完整版**
   - 配置DeepSeek API密钥到 `backend/.env`
   - 双击运行 `start_system.bat`

### 其他系统

#### macOS
```bash
# 使用Homebrew安装
brew install python3

# 或下载官方安装包
# https://www.python.org/downloads/macos/
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install python3 python3-pip
```

## 功能对比

| 功能 | 离线模式 | 完整版 |
|------|----------|--------|
| 基础诊断 | ✅ | ✅ |
| 头痛症状 | ✅ | ✅ |
| 咳嗽症状 | ✅ | ✅ |
| 胃痛症状 | ✅ | ✅ |
| 其他症状 | ❌ | ✅ |
| AI智能分析 | ❌ | ✅ |
| 个性化诊断 | ❌ | ✅ |

## 推荐使用方式

1. **新手用户**：直接使用离线模式体验系统功能
2. **专业用户**：安装Python后使用完整版获得更强大的功能
3. **开发者**：可以基于现有代码进行二次开发

## 常见问题

### Q: 为什么显示"网络连接失败"？
A: 这是正常的，系统会自动切换到离线模式，您可以继续使用。

### Q: 离线模式支持哪些症状？
A: 目前支持头痛、咳嗽、胃痛等常见症状，其他症状会给出通用建议。

### Q: 如何获得更多症状的诊断？
A: 安装Python环境并配置DeepSeek API后，可以支持所有症状的智能诊断。

### Q: 诊断结果准确吗？
A: 本系统仅供参考学习，不能替代专业医师诊断，请在专业中医师指导下治疗。

## 技术支持

如果您在使用过程中遇到问题：
1. 查看 `README.md` 获取详细说明
2. 查看 `USAGE_EXAMPLES.md` 了解使用示例
3. 检查浏览器控制台是否有错误信息
