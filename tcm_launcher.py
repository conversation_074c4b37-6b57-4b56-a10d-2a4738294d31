#!/usr/bin/env python3
"""
中医智慧诊疗系统 - 独立启动器
支持演示版和完整版的统一启动
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import subprocess
import webbrowser
import time
import requests
from pathlib import Path

class TCMLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("中医智慧诊疗系统 v2.0")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 设置图标（如果存在）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
            
        self.backend_process = None
        self.current_mode = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🏥 中医智慧诊疗系统", 
            font=("微软雅黑", 20, "bold"),
            fg="white", 
            bg="#2c3e50"
        )
        title_label.pack(expand=True)
        
        # 主内容区域
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        # 版本选择区域
        version_frame = tk.LabelFrame(main_frame, text="选择版本", font=("微软雅黑", 12, "bold"))
        version_frame.pack(fill="x", pady=(0, 20))
        
        # 演示版按钮
        demo_frame = tk.Frame(version_frame, padx=10, pady=10)
        demo_frame.pack(fill="x")
        
        demo_btn = tk.Button(
            demo_frame,
            text="🎯 启动演示版",
            font=("微软雅黑", 12, "bold"),
            bg="#27ae60",
            fg="white",
            height=2,
            command=self.start_demo_version
        )
        demo_btn.pack(side="left", padx=(0, 10))
        
        demo_info = tk.Label(
            demo_frame,
            text="• 无需配置，即开即用\n• 内置知识库\n• 支持常见症状",
            font=("微软雅黑", 9),
            justify="left"
        )
        demo_info.pack(side="left")
        
        # 完整版按钮
        full_frame = tk.Frame(version_frame, padx=10, pady=10)
        full_frame.pack(fill="x")
        
        full_btn = tk.Button(
            full_frame,
            text="🚀 启动完整版",
            font=("微软雅黑", 12, "bold"),
            bg="#3498db",
            fg="white",
            height=2,
            command=self.start_full_version
        )
        full_btn.pack(side="left", padx=(0, 10))
        
        full_info = tk.Label(
            full_frame,
            text="• DeepSeek AI智能诊断\n• 支持所有症状\n• 需要API密钥",
            font=("微软雅黑", 9),
            justify="left"
        )
        full_info.pack(side="left")
        
        # 状态显示区域
        status_frame = tk.LabelFrame(main_frame, text="系统状态", font=("微软雅黑", 12, "bold"))
        status_frame.pack(fill="x", pady=(0, 20))
        
        self.status_text = tk.Text(
            status_frame, 
            height=8, 
            font=("Consolas", 9),
            bg="#f8f9fa",
            state="disabled"
        )
        self.status_text.pack(fill="x", padx=10, pady=10)
        
        # 控制按钮区域
        control_frame = tk.Frame(main_frame)
        control_frame.pack(fill="x")
        
        # 打开前端按钮
        open_btn = tk.Button(
            control_frame,
            text="🌐 打开前端界面",
            font=("微软雅黑", 10),
            bg="#e74c3c",
            fg="white",
            command=self.open_frontend
        )
        open_btn.pack(side="left", padx=(0, 10))
        
        # API配置按钮
        config_btn = tk.Button(
            control_frame,
            text="⚙️ 配置API",
            font=("微软雅黑", 10),
            command=self.configure_api
        )
        config_btn.pack(side="left", padx=(0, 10))
        
        # 停止服务按钮
        stop_btn = tk.Button(
            control_frame,
            text="⏹️ 停止服务",
            font=("微软雅黑", 10),
            bg="#95a5a6",
            fg="white",
            command=self.stop_service
        )
        stop_btn.pack(side="right")
        
        # 初始状态
        self.log_message("🏥 中医智慧诊疗系统已就绪")
        self.log_message("请选择要启动的版本...")
        
    def log_message(self, message):
        """添加日志消息"""
        self.status_text.config(state="normal")
        self.status_text.insert("end", f"[{time.strftime('%H:%M:%S')}] {message}\n")
        self.status_text.see("end")
        self.status_text.config(state="disabled")
        self.root.update()
        
    def start_demo_version(self):
        """启动演示版"""
        self.log_message("🎯 正在启动演示版...")
        self.current_mode = "demo"
        
        def run_demo():
            try:
                # 导入演示版模块
                sys.path.append(os.path.join(os.path.dirname(__file__), "backend"))
                from demo_app import app
                
                self.log_message("✅ 演示版后端启动成功")
                self.log_message("📍 服务地址: http://localhost:5000")
                self.log_message("💡 特点: 内置知识库，支持常见症状")
                
                app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
                
            except Exception as e:
                self.log_message(f"❌ 演示版启动失败: {str(e)}")
                
        # 在新线程中启动
        threading.Thread(target=run_demo, daemon=True).start()
        
    def start_full_version(self):
        """启动完整版"""
        # 检查API配置
        env_file = os.path.join(os.path.dirname(__file__), "backend", ".env")
        if not os.path.exists(env_file):
            messagebox.showwarning(
                "配置缺失", 
                "未找到API配置文件，请先配置DeepSeek API密钥"
            )
            self.configure_api()
            return
            
        self.log_message("🚀 正在启动完整版...")
        self.current_mode = "full"
        
        def run_full():
            try:
                # 导入完整版模块
                sys.path.append(os.path.join(os.path.dirname(__file__), "backend"))
                from app import app
                
                self.log_message("✅ 完整版后端启动成功")
                self.log_message("📍 服务地址: http://localhost:5000")
                self.log_message("🤖 特点: DeepSeek AI智能诊断")
                
                app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
                
            except Exception as e:
                self.log_message(f"❌ 完整版启动失败: {str(e)}")
                if "DEEPSEEK_API_KEY" in str(e):
                    self.log_message("💡 请检查DeepSeek API密钥配置")
                
        # 在新线程中启动
        threading.Thread(target=run_full, daemon=True).start()
        
    def open_frontend(self):
        """打开前端界面"""
        frontend_path = os.path.join(os.path.dirname(__file__), "frontend", "index.html")
        if os.path.exists(frontend_path):
            webbrowser.open(f"file:///{os.path.abspath(frontend_path)}")
            self.log_message("🌐 前端界面已在浏览器中打开")
        else:
            messagebox.showerror("文件缺失", "未找到前端文件")
            
    def configure_api(self):
        """配置API密钥"""
        config_window = tk.Toplevel(self.root)
        config_window.title("DeepSeek API 配置")
        config_window.geometry("500x300")
        config_window.resizable(False, False)
        
        # 说明文本
        info_label = tk.Label(
            config_window,
            text="请输入您的DeepSeek API密钥：",
            font=("微软雅黑", 12)
        )
        info_label.pack(pady=10)
        
        # API密钥输入
        api_frame = tk.Frame(config_window)
        api_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(api_frame, text="API密钥:", font=("微软雅黑", 10)).pack(anchor="w")
        api_entry = tk.Entry(api_frame, font=("Consolas", 10), width=60)
        api_entry.pack(fill="x", pady=5)
        
        # 获取现有配置
        env_file = os.path.join(os.path.dirname(__file__), "backend", ".env")
        if os.path.exists(env_file):
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for line in content.split('\n'):
                        if line.startswith('DEEPSEEK_API_KEY='):
                            current_key = line.split('=', 1)[1]
                            if current_key != "your_deepseek_api_key_here":
                                api_entry.insert(0, current_key)
                            break
            except:
                pass
        
        # 说明文本
        help_text = tk.Text(config_window, height=6, font=("微软雅黑", 9))
        help_text.pack(fill="x", padx=20, pady=10)
        help_text.insert("1.0", """获取API密钥步骤：
1. 访问 https://platform.deepseek.com/
2. 注册账号并完成验证
3. 进入控制台 → API密钥页面
4. 创建新密钥并复制（格式：sk-xxxxxxxxxxxxxxxx）
5. 粘贴到上方输入框中""")
        help_text.config(state="disabled")
        
        # 按钮
        btn_frame = tk.Frame(config_window)
        btn_frame.pack(fill="x", padx=20, pady=10)
        
        def save_config():
            api_key = api_entry.get().strip()
            if not api_key:
                messagebox.showerror("错误", "请输入API密钥")
                return
                
            if not api_key.startswith("sk-"):
                messagebox.showerror("错误", "API密钥格式错误，应以 sk- 开头")
                return
                
            # 保存配置
            env_content = f"""# DeepSeek API 配置
DEEPSEEK_API_KEY={api_key}
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 配置说明：
# 1. 访问 https://platform.deepseek.com/ 注册账号
# 2. 获取API密钥（格式：sk-xxxxxxxxxxxxxxxx）
# 3. 将上面的 your_deepseek_api_key_here 替换为您的实际API密钥
# 4. 保存此文件
"""
            
            try:
                backend_dir = os.path.join(os.path.dirname(__file__), "backend")
                os.makedirs(backend_dir, exist_ok=True)
                
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write(env_content)
                    
                messagebox.showinfo("成功", "API配置已保存")
                config_window.destroy()
                self.log_message("⚙️ DeepSeek API配置已更新")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
        
        tk.Button(
            btn_frame,
            text="保存配置",
            font=("微软雅黑", 10),
            bg="#27ae60",
            fg="white",
            command=save_config
        ).pack(side="right", padx=(10, 0))
        
        tk.Button(
            btn_frame,
            text="取消",
            font=("微软雅黑", 10),
            command=config_window.destroy
        ).pack(side="right")
        
    def stop_service(self):
        """停止服务"""
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process = None
            
        self.log_message("⏹️ 服务已停止")
        self.current_mode = None
        
    def run(self):
        """运行启动器"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """关闭程序时的处理"""
        if self.backend_process:
            self.backend_process.terminate()
        self.root.destroy()

if __name__ == "__main__":
    launcher = TCMLauncher()
    launcher.run()
