中医智慧诊疗系统 v2.0 - 使用说明
========================================

🚀 快速开始
----------
1. 双击运行 "中医智慧诊疗系统.exe"
2. 选择要启动的版本：
   - 演示版：无需配置，即开即用
   - 完整版：需要DeepSeek API密钥
3. 点击"打开前端界面"按钮访问诊疗系统

📋 版本对比
----------
┌─────────────┬──────────┬──────────┐
│    功能     │  演示版  │  完整版  │
├─────────────┼──────────┼──────────┤
│ 基础诊断    │    ✅    │    ✅    │
│ 脉象舌象    │    ✅    │    ✅    │
│ 支持症状    │ 常见症状 │ 所有症状 │
│ AI智能分析  │    ❌    │    ✅    │
│ 个性化诊断  │    ❌    │    ✅    │
│ 复杂症状    │   有限   │   无限   │
└─────────────┴──────────┴──────────┘

🎯 演示版特点
------------
• 无需任何配置，双击即可使用
• 内置中医知识库，支持常见症状
• 支持头痛、咳嗽、胃痛等症状诊断
• 完整的脉象舌象功能
• 专业的中医诊疗建议

🚀 完整版特点
------------
• 集成DeepSeek AI，智能诊断
• 支持所有症状类型
• 个性化诊疗建议
• 复杂症状组合分析
• 更准确的诊断结果

⚙️ 完整版配置步骤
----------------
1. 访问 https://platform.deepseek.com/
2. 注册账号并完成邮箱验证
3. 进入控制台 → API密钥页面
4. 创建新密钥并复制（格式：sk-xxxxxxxxxxxxxxxx）
5. 在程序中点击"配置API"按钮
6. 输入API密钥并保存
7. 重新启动完整版

🌐 前端界面使用
--------------
1. 程序启动后，点击"打开前端界面"按钮
2. 或直接在浏览器中打开：_internal/frontend/index.html
3. 填写患者信息（性别、年龄）
4. 选择症状、脉象、舌象
5. 点击"获取中医诊断建议"
6. 查看完整的诊疗建议

💡 使用技巧
----------
• 详细描述症状：包括发病时间、症状特点、伴随症状
• 准确选择脉象舌象：这会显著提高诊断准确性
• 提供患者信息：性别、年龄等信息有助于个性化诊断
• 尝试复杂症状：完整版支持多系统、多症状综合分析

📝 示例症状输入
--------------
简单症状：
"头痛三天，伴有恶心"

复杂症状：
"患者女性，28岁，月经不调3个月，经期延后7-10天，
经量偏少，色淡质稀，小腹隐痛，腰膝酸软，
畏寒肢冷，夜尿频多，舌质淡胖有齿痕，苔薄白，脉沉细"

🔧 故障排除
----------
问题1：程序无法启动
解决：检查是否有杀毒软件阻止，尝试以管理员身份运行

问题2：完整版提示API错误
解决：检查API密钥是否正确，网络连接是否正常

问题3：前端界面无法打开
解决：检查浏览器设置，尝试手动打开 _internal/frontend/index.html

问题4：诊断结果显示异常
解决：重启程序，检查症状描述是否完整

📞 技术支持
----------
• 项目地址：https://github.com/your-repo
• 问题反馈：请在GitHub提交Issue
• 使用文档：查看项目README.md

⚠️ 重要提醒
----------
• 本系统仅提供中医理论参考，不能替代专业医师诊断
• 所有治疗建议仅供参考，请在专业中医师指导下进行治疗
• 如有严重症状，请及时就医

📦 系统信息
----------
版本：v2.0
打包时间：2025年5月26日
支持系统：Windows 10/11
文件大小：约100MB
运行环境：无需安装Python

🎉 开始使用
----------
配置完成后，您就可以体验专业的中医智慧诊疗系统了！
祝您使用愉快！ 🏥✨
