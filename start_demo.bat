@echo off
echo ========================================
echo 中医智慧诊疗系统 - 演示版启动脚本
echo ========================================

echo.
echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 安装必要的Python包...
cd backend
pip install flask flask-cors >nul 2>&1

echo.
echo 启动演示版后端服务...
echo.
echo ========================================
echo 🏥 中医智慧诊疗系统 - 演示版
echo ========================================
echo.
echo 📍 后端服务: http://localhost:5000
echo 📍 前端页面: 请打开 frontend\index.html
echo.
echo 💡 演示版特点:
echo    - 无需API密钥，即开即用
echo    - 支持头痛、咳嗽、胃痛等症状
echo    - 内置中医知识库
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

python demo_app.py
