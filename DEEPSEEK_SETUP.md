# DeepSeek API 完整版配置指南

## 🎯 目标
配置并运行完整版中医智慧诊疗系统，调用DeepSeek API获得更智能的诊断。

## 📋 前置要求

### 1. Python环境
确保您的系统已安装Python 3.8+：
- **Windows**: 从 https://python.org 下载安装
- **macOS**: `brew install python3`
- **Linux**: `sudo apt install python3 python3-pip`

### 2. DeepSeek API账号
- 访问：https://platform.deepseek.com/
- 注册账号并获取API密钥

## 🔧 配置步骤

### 第一步：获取DeepSeek API密钥

1. **注册账号**
   - 访问 https://platform.deepseek.com/
   - 点击"注册"创建账号
   - 完成邮箱验证

2. **获取API密钥**
   - 登录后进入控制台
   - 找到"API密钥"或"API Keys"页面
   - 点击"创建新密钥"
   - 复制生成的API密钥（格式类似：sk-xxxxxxxxxxxxxxxx）

### 第二步：配置环境变量

1. **复制配置文件**
   ```bash
   cd backend
   copy .env.example .env
   ```

2. **编辑配置文件**
   打开 `backend/.env` 文件，修改：
   ```
   DEEPSEEK_API_KEY=sk-your-actual-api-key-here
   DEEPSEEK_BASE_URL=https://api.deepseek.com
   ```

### 第三步：安装Python依赖

```bash
cd backend
pip install -r requirements.txt
```

如果遇到权限问题，使用：
```bash
pip install --user -r requirements.txt
```

### 第四步：启动后端服务

```bash
cd backend
python app.py
```

成功启动后会看到：
```
* Running on http://127.0.0.1:5000
* Debug mode: on
```

### 第五步：访问前端

打开浏览器访问 `frontend/index.html`，系统会自动连接到后端API。

## 🚀 快速启动脚本

### Windows用户
双击运行 `start_system.bat`

### 手动启动
```bash
# 1. 进入后端目录
cd backend

# 2. 安装依赖（首次运行）
pip install flask flask-cors requests python-dotenv openai

# 3. 启动服务
python app.py
```

## 🔍 验证配置

### 检查API连接
访问：http://localhost:5000/
应该看到：
```json
{
  "status": "healthy",
  "message": "中医智慧诊疗系统后端服务运行正常",
  "service_available": true
}
```

### 测试诊断功能
1. 打开 `frontend/index.html`
2. 输入症状："头痛三天，伴有恶心"
3. 选择脉象："弦脉"
4. 选择舌象："舌质红，舌苔薄黄"
5. 点击"获取中医诊断建议"

## 🆚 完整版 vs 离线版对比

| 功能 | 离线版 | 完整版 |
|------|--------|--------|
| 基础诊断 | ✅ | ✅ |
| 脉象舌象 | ✅ | ✅ |
| 支持症状 | 头痛、咳嗽、胃痛 | 所有症状 |
| AI智能分析 | ❌ | ✅ |
| 个性化诊断 | ❌ | ✅ |
| 复杂症状组合 | 有限 | 无限制 |
| 诊断准确性 | 基础 | 高级 |

## 🔧 故障排除

### 问题1：Python未安装
**症状**：命令行提示"python不是内部或外部命令"
**解决**：
1. 下载安装Python：https://python.org
2. 安装时勾选"Add Python to PATH"
3. 重启命令行

### 问题2：API密钥错误
**症状**：返回401 Unauthorized错误
**解决**：
1. 检查API密钥是否正确
2. 确认密钥格式：sk-xxxxxxxxxxxxxxxx
3. 检查账号是否有余额

### 问题3：网络连接失败
**症状**：连接超时或网络错误
**解决**：
1. 检查网络连接
2. 确认防火墙设置
3. 尝试使用代理

### 问题4：依赖安装失败
**症状**：pip install报错
**解决**：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
```

## 📊 API使用监控

### 查看API使用情况
- 登录DeepSeek控制台
- 查看"使用统计"页面
- 监控API调用次数和费用

### 成本控制
- 设置使用限额
- 监控每日调用量
- 合理使用API避免超额

## 🎓 使用建议

### 最佳实践
1. **详细描述症状**：提供越详细的症状，AI分析越准确
2. **结合脉象舌象**：使用新的脉象舌象功能提高诊断质量
3. **多次测试**：尝试不同的症状组合
4. **学习参考**：将结果作为学习中医的参考

### 注意事项
- 本系统仅供学习参考，不能替代专业医师诊断
- 所有治疗建议需在专业中医师指导下执行
- 如有严重症状，请及时就医

## 🚀 开始使用

配置完成后，您就可以体验完整版的中医智慧诊疗系统了！

**祝您使用愉快！** 🏥✨
