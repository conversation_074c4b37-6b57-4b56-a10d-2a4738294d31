@echo off
echo ========================================
echo 启动中医智慧诊疗系统后端服务
echo ========================================

echo.
echo 正在启动后端服务...
echo.

cd backend

REM 尝试不同的Python命令
echo 尝试使用 python 命令...
python app.py 2>nul
if %errorlevel% equ 0 goto :success

echo 尝试使用 py 命令...
py app.py 2>nul
if %errorlevel% equ 0 goto :success

echo 尝试使用 python3 命令...
python3 app.py 2>nul
if %errorlevel% equ 0 goto :success

echo.
echo ❌ 无法启动Python服务
echo.
echo 请确保：
echo 1. Python 3.10 已正确安装
echo 2. Python已添加到PATH环境变量
echo 3. 已安装必要的依赖包
echo.
echo 手动安装依赖：
echo python -m pip install flask flask-cors requests python-dotenv openai
echo.
echo 然后手动启动：
echo python app.py
echo.
pause
exit /b 1

:success
echo ✅ 服务启动成功！
