// 中医智慧诊疗系统前端JavaScript

const API_BASE_URL = 'http://localhost:5000/api';

// 检测是否为离线模式（后端不可用时）
let isOfflineMode = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCommonSymptoms();
});

// 内置的常见症状数据（离线模式使用）
const offlineSymptoms = [
    "头痛、头晕",
    "咳嗽、咳痰",
    "胃痛、腹胀",
    "失眠、多梦",
    "腰膝酸软",
    "月经不调",
    "便秘、腹泻",
    "心悸、胸闷",
    "疲劳乏力",
    "食欲不振",
    "手脚冰凉",
    "口干舌燥",
    "情绪焦虑",
    "关节疼痛",
    "皮肤瘙痒"
];

// 脉象数据
const pulseTypes = [
    { name: "浮脉", description: "轻取即得，重按稍减" },
    { name: "沉脉", description: "轻取不应，重按始得" },
    { name: "迟脉", description: "脉来缓慢，一息不足四至" },
    { name: "数脉", description: "脉来急疾，一息五六至" },
    { name: "滑脉", description: "往来流利，如珠走盘" },
    { name: "涩脉", description: "往来艰涩，如轻刀刮竹" },
    { name: "弦脉", description: "端直以长，如按琴弦" },
    { name: "紧脉", description: "如转绳索，左右弹指" },
    { name: "缓脉", description: "脉来和缓，一息四至" },
    { name: "洪脉", description: "脉大而实，如波涛汹涌" },
    { name: "细脉", description: "脉细如线，但应指明显" },
    { name: "弱脉", description: "脉细而软，重按欲绝" },
    { name: "虚脉", description: "三部脉举之无力，按之空虚" },
    { name: "实脉", description: "三部脉举按皆有力" }
];

// 舌质数据
const tongueQualities = [
    { name: "淡红", description: "正常舌质" },
    { name: "淡白", description: "气血不足" },
    { name: "红", description: "热证" },
    { name: "绛红", description: "热盛" },
    { name: "紫暗", description: "血瘀" },
    { name: "胖大", description: "脾虚湿盛" },
    { name: "瘦薄", description: "阴虚火旺" },
    { name: "有齿痕", description: "脾虚" },
    { name: "有瘀斑", description: "血瘀" }
];

// 舌苔数据
const tongueCoatings = [
    { name: "薄白", description: "正常或表证" },
    { name: "厚白", description: "寒湿" },
    { name: "薄黄", description: "热证初起" },
    { name: "厚黄", description: "湿热" },
    { name: "白腻", description: "寒湿痰浊" },
    { name: "黄腻", description: "湿热痰浊" },
    { name: "少苔", description: "阴虚" },
    { name: "无苔", description: "胃阴枯竭" },
    { name: "剥苔", description: "胃气大伤" },
    { name: "黑苔", description: "热极或寒极" }
];

// 存储用户选择的脉象和舌象
let selectedPulse = '';
let selectedTongueQuality = '';
let selectedTongueCoating = '';

// 存储当前诊断结果（用于导出）
let currentDiagnosisData = {
    patientInfo: {},
    symptoms: '',
    pulse: '',
    tongue: '',
    diagnosis: '',
    timestamp: ''
};

// 获取患者基本信息
function getPatientInfo() {
    const genderRadio = document.querySelector('input[name="gender"]:checked');
    const ageInput = document.getElementById('age');

    return {
        gender: genderRadio ? genderRadio.value : '',
        age: ageInput ? ageInput.value : '',
        hasInfo: (genderRadio && genderRadio.value) || (ageInput && ageInput.value)
    };
}

// 加载常见症状
async function loadCommonSymptoms() {
    try {
        const response = await fetch(`${API_BASE_URL}/common-symptoms`);
        const data = await response.json();

        if (data.success) {
            displaySymptoms(data.symptoms);
        }
    } catch (error) {
        console.error('后端服务不可用，切换到离线模式:', error);
        isOfflineMode = true;
        displaySymptoms(offlineSymptoms);
        showOfflineModeNotice();
    }

    // 加载脉象和舌象选项（无论在线还是离线模式都显示）
    loadPulseOptions();
    loadTongueOptions();
}

// 显示症状按钮
function displaySymptoms(symptoms) {
    const container = document.getElementById('commonSymptoms');
    container.innerHTML = '';

    symptoms.forEach(symptom => {
        const button = document.createElement('button');
        button.className = 'px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-300 transition duration-200';
        button.textContent = symptom;
        button.onclick = () => addSymptom(symptom);
        container.appendChild(button);
    });
}

// 加载脉象选项
function loadPulseOptions() {
    const container = document.getElementById('pulseOptions');
    container.innerHTML = '';

    pulseTypes.forEach(pulse => {
        const button = document.createElement('button');
        button.className = 'px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition duration-200';
        button.textContent = pulse.name;
        button.title = pulse.description;
        button.onclick = () => selectPulse(pulse.name, button);
        container.appendChild(button);
    });
}

// 加载舌象选项
function loadTongueOptions() {
    // 加载舌质选项
    const qualityContainer = document.getElementById('tongueQualityOptions');
    qualityContainer.innerHTML = '';

    tongueQualities.forEach(quality => {
        const button = document.createElement('button');
        button.className = 'px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition duration-200';
        button.textContent = quality.name;
        button.title = quality.description;
        button.onclick = () => selectTongueQuality(quality.name, button);
        qualityContainer.appendChild(button);
    });

    // 加载舌苔选项
    const coatingContainer = document.getElementById('tongueCoatingOptions');
    coatingContainer.innerHTML = '';

    tongueCoatings.forEach(coating => {
        const button = document.createElement('button');
        button.className = 'px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs hover:bg-purple-200 transition duration-200';
        button.textContent = coating.name;
        button.title = coating.description;
        button.onclick = () => selectTongueCoating(coating.name, button);
        coatingContainer.appendChild(button);
    });
}

// 选择脉象
function selectPulse(pulseName, buttonElement) {
    // 清除之前的选择
    document.querySelectorAll('#pulseOptions button').forEach(btn => {
        btn.className = btn.className.replace('bg-blue-500 text-white', 'bg-blue-100 text-blue-700');
    });

    // 高亮当前选择
    buttonElement.className = buttonElement.className.replace('bg-blue-100 text-blue-700', 'bg-blue-500 text-white');
    selectedPulse = pulseName;

    // 添加到症状描述
    addToSymptoms(`脉象${pulseName}`);
}

// 选择舌质
function selectTongueQuality(qualityName, buttonElement) {
    // 清除之前的选择
    document.querySelectorAll('#tongueQualityOptions button').forEach(btn => {
        btn.className = btn.className.replace('bg-green-500 text-white', 'bg-green-100 text-green-700');
    });

    // 高亮当前选择
    buttonElement.className = buttonElement.className.replace('bg-green-100 text-green-700', 'bg-green-500 text-white');
    selectedTongueQuality = qualityName;

    // 添加到症状描述
    addToSymptoms(`舌质${qualityName}`);
}

// 选择舌苔
function selectTongueCoating(coatingName, buttonElement) {
    // 清除之前的选择
    document.querySelectorAll('#tongueCoatingOptions button').forEach(btn => {
        btn.className = btn.className.replace('bg-purple-500 text-white', 'bg-purple-100 text-purple-700');
    });

    // 高亮当前选择
    buttonElement.className = buttonElement.className.replace('bg-purple-100 text-purple-700', 'bg-purple-500 text-white');
    selectedTongueCoating = coatingName;

    // 添加到症状描述
    addToSymptoms(`舌苔${coatingName}`);
}

// 显示离线模式提示
function showOfflineModeNotice() {
    const notice = document.createElement('div');
    notice.className = 'bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4';
    notice.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <span class="text-yellow-400">⚠️</span>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">离线演示模式</h3>
                <div class="mt-1 text-sm text-yellow-700">
                    后端服务未启动，当前使用内置演示数据。支持头痛、咳嗽、胃痛等症状的模拟诊断。
                </div>
            </div>
        </div>
    `;

    // 插入到症状描述区域前面
    const symptomsSection = document.querySelector('.max-w-4xl');
    symptomsSection.insertBefore(notice, symptomsSection.firstChild);
}

// 添加症状到输入框
function addSymptom(symptom) {
    addToSymptoms(symptom);
}

// 添加内容到症状描述框
function addToSymptoms(content) {
    const textarea = document.getElementById('symptoms');
    const currentValue = textarea.value.trim();

    if (currentValue) {
        textarea.value = currentValue + '，' + content;
    } else {
        textarea.value = content;
    }

    // 聚焦到输入框
    textarea.focus();
}

// 获取诊断建议
async function getDiagnosis() {
    const symptoms = document.getElementById('symptoms').value.trim();

    if (!symptoms) {
        showError('请输入症状描述');
        return;
    }

    // 获取患者基本信息
    const patientInfo = getPatientInfo();

    // 构建完整的症状描述
    let fullSymptoms = symptoms;
    if (patientInfo.hasInfo) {
        let patientDesc = '患者';
        if (patientInfo.gender) {
            patientDesc += patientInfo.gender + '性';
        }
        if (patientInfo.age) {
            patientDesc += '，' + patientInfo.age + '岁';
        }
        fullSymptoms = patientDesc + '，' + symptoms;
    }

    // 显示加载状态
    setLoading(true);
    hideError();
    hideResult();

    if (isOfflineMode) {
        // 离线模式：使用内置诊断逻辑
        setTimeout(() => {
            const diagnosis = getOfflineDiagnosis(fullSymptoms);
            showResult(diagnosis);
            setLoading(false);
        }, 1500); // 模拟网络延迟
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/diagnosis`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ symptoms: fullSymptoms })
        });

        const data = await response.json();

        if (data.success) {
            showResult(data.diagnosis);
        } else {
            showError(data.error || '获取诊断建议失败');
        }
    } catch (error) {
        console.error('请求失败，切换到离线模式:', error);
        isOfflineMode = true;
        if (!document.querySelector('.bg-yellow-50')) {
            showOfflineModeNotice();
        }

        // 使用离线诊断
        setTimeout(() => {
            const diagnosis = getOfflineDiagnosis(fullSymptoms);
            showResult(diagnosis);
            setLoading(false);
        }, 1000);
        return;
    }

    setLoading(false);
}

// 设置加载状态
function setLoading(isLoading) {
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const loadingSpinner = document.getElementById('loadingSpinner');

    if (isLoading) {
        submitBtn.disabled = true;
        submitBtn.className = submitBtn.className.replace('hover:bg-blue-700', 'opacity-50 cursor-not-allowed');
        submitText.textContent = '正在分析中...';
        loadingSpinner.classList.remove('hidden');
    } else {
        submitBtn.disabled = false;
        submitBtn.className = submitBtn.className.replace('opacity-50 cursor-not-allowed', 'hover:bg-blue-700');
        submitText.textContent = '获取中医诊断建议';
        loadingSpinner.classList.add('hidden');
    }
}

// 显示诊断结果
function showResult(diagnosis) {
    const resultArea = document.getElementById('resultArea');
    const diagnosisResult = document.getElementById('diagnosisResult');

    // 保存诊断数据用于导出
    saveDiagnosisData(diagnosis);

    // 将Markdown格式的文本转换为HTML
    const htmlContent = formatDiagnosisText(diagnosis);
    diagnosisResult.innerHTML = htmlContent;

    resultArea.classList.remove('hidden');

    // 滚动到结果区域
    resultArea.scrollIntoView({ behavior: 'smooth' });
}

// 格式化诊断文本
function formatDiagnosisText(text) {
    // 简单的Markdown到HTML转换
    let html = text
        // 处理标题
        .replace(/^## (.*$)/gm, '<h2 class="text-lg font-semibold mt-6 mb-3 text-gray-800 border-b border-gray-200 pb-2">$1</h2>')
        .replace(/^### (.*$)/gm, '<h3 class="text-md font-medium mt-4 mb-2 text-gray-700">$1</h3>')
        // 处理段落
        .replace(/\n\n/g, '</p><p class="mb-3">')
        // 处理列表项
        .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')
        // 处理粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 处理换行
        .replace(/\n/g, '<br>');

    // 包装在段落标签中
    html = '<p class="mb-3">' + html + '</p>';

    return html;
}

// 显示错误信息
function showError(message) {
    const errorArea = document.getElementById('errorArea');
    const errorMessage = document.getElementById('errorMessage');

    errorMessage.textContent = message;
    errorArea.classList.remove('hidden');

    // 滚动到错误区域
    errorArea.scrollIntoView({ behavior: 'smooth' });
}

// 隐藏错误信息
function hideError() {
    const errorArea = document.getElementById('errorArea');
    errorArea.classList.add('hidden');
}

// 隐藏结果
function hideResult() {
    const resultArea = document.getElementById('resultArea');
    resultArea.classList.add('hidden');
}

// 离线诊断逻辑
function getOfflineDiagnosis(symptoms) {
    const symptomsLower = symptoms.toLowerCase();

    // 提取患者基本信息
    const patientInfo = extractPatientInfo(symptoms);

    // 分析脉象和舌象信息
    const pulseInfo = extractPulseInfo(symptoms);
    const tongueInfo = extractTongueInfo(symptoms);

    // 头痛相关症状
    if (symptomsLower.includes('头痛') || symptomsLower.includes('头疼') || symptomsLower.includes('头晕')) {
        const baseDiagnosis = `## 1. 中医诊断
风寒头痛

## 2. 疾病机理
风寒外袭，经络阻滞，清阳不升

## 3. 治疗方案
疏风散寒，通络止痛

## 4. 推荐方剂
川芎茶调散加减

## 5. 中药处方分析
### 君药：川芎12g - 活血行气，祛风止痛
### 臣药：白芷9g - 祛风解表，通窍止痛；荆芥9g - 祛风解表
### 佐药：防风6g - 祛风胜湿；薄荷6g - 疏散风热
### 使药：甘草3g - 调和诸药；茶叶3g - 清头目

## 6. 针灸治疗
### 主要经络：督脉、足太阳膀胱经
### 推荐穴位：百会、风池、太阳、合谷
### 针灸方法：平补平泻，留针20-30分钟

## 7. 注意事项
### 饮食调理：忌食生冷、辛辣刺激性食物
### 生活起居：注意头部保暖，避免风寒
### 禁忌事项：孕妇慎用
### 复诊建议：3-5天后复诊

**重要提醒：以上建议仅供参考，请在专业中医师指导下进行治疗。**`;

        return adjustDiagnosisWithPatientInfo(baseDiagnosis, patientInfo, pulseInfo, tongueInfo);
    }

    // 咳嗽相关症状
    if (symptomsLower.includes('咳嗽') || symptomsLower.includes('咳痰') || symptomsLower.includes('咳喘')) {
        const baseDiagnosis = `## 1. 中医诊断
风寒咳嗽

## 2. 疾病机理
风寒犯肺，肺气失宣，津液凝聚成痰

## 3. 治疗方案
疏风散寒，宣肺止咳

## 4. 推荐方剂
三拗汤加减

## 5. 中药处方分析
### 君药：麻黄6g - 宣肺平喘，发汗解表
### 臣药：杏仁9g - 降气止咳；桔梗9g - 宣肺祛痰
### 佐药：紫苏叶9g - 理气宽中；陈皮6g - 理气化痰
### 使药：甘草3g - 调和诸药，润肺止咳

## 6. 针灸治疗
### 主要经络：手太阴肺经、督脉
### 推荐穴位：肺俞、列缺、尺泽、天突
### 针灸方法：补法为主，留针25分钟

## 7. 注意事项
### 饮食调理：忌食寒凉、油腻食物，多饮温水
### 生活起居：注意保暖，避免受凉
### 禁忌事项：高血压患者慎用麻黄
### 复诊建议：一周后复诊

**重要提醒：以上建议仅供参考，请在专业中医师指导下进行治疗。**`;

        return adjustDiagnosisWithPatientInfo(baseDiagnosis, patientInfo, pulseInfo, tongueInfo);
    }

    // 胃痛相关症状
    if (symptomsLower.includes('胃痛') || symptomsLower.includes('胃胀') || symptomsLower.includes('腹痛') || symptomsLower.includes('消化不良')) {
        const baseDiagnosis = `## 1. 中医诊断
脾胃虚寒

## 2. 疾病机理
脾胃阳虚，寒从中生，胃失温煦

## 3. 治疗方案
温中健脾，和胃止痛

## 4. 推荐方剂
理中汤加减

## 5. 中药处方分析
### 君药：干姜9g - 温中散寒
### 臣药：白术12g - 健脾燥湿；人参9g - 大补元气
### 佐药：陈皮6g - 理气和胃；木香6g - 行气止痛
### 使药：甘草6g - 调和诸药，缓急止痛

## 6. 针灸治疗
### 主要经络：足阳明胃经、足太阴脾经
### 推荐穴位：中脘、足三里、脾俞、胃俞
### 针灸方法：温针灸，留针30分钟

## 7. 注意事项
### 饮食调理：温热易消化食物，少食多餐
### 生活起居：腹部保暖，规律作息
### 禁忌事项：忌食生冷、辛辣食物
### 复诊建议：两周后复诊

**重要提醒：以上建议仅供参考，请在专业中医师指导下进行治疗。**`;

        return adjustDiagnosisWithPatientInfo(baseDiagnosis, patientInfo, pulseInfo, tongueInfo);
    }

    // 通用建议
    return `## 中医诊疗建议

### 症状描述
您描述的症状：${symptoms}

### 一般性建议
根据中医理论，建议您：

1. **辨证论治**：中医讲究个体化治疗，需要结合您的体质、舌象、脉象等进行综合分析
2. **调理脾胃**：脾胃为后天之本，建议规律饮食，避免生冷
3. **情志调节**：保持心情舒畅，避免过度焦虑
4. **适度运动**：根据体质选择合适的运动方式
5. **作息规律**：早睡早起，顺应自然规律

### 建议就诊
由于症状的复杂性，建议您：
- 及时就诊专业中医师
- 进行详细的中医四诊（望、闻、问、切）
- 根据具体情况制定个性化治疗方案

### 离线演示说明
当前为离线演示模式，支持的症状关键词：
- **头痛相关**：头痛、头疼、头晕
- **咳嗽相关**：咳嗽、咳痰、咳喘
- **胃痛相关**：胃痛、胃胀、腹痛、消化不良

**免责声明：本系统仅提供中医理论参考，不能替代专业医师诊断。**`;
}

// 提取患者基本信息
function extractPatientInfo(symptoms) {
    const patientInfo = {
        gender: '',
        age: '',
        description: ''
    };

    // 提取性别信息
    if (symptoms.includes('患者男性') || symptoms.includes('男性')) {
        patientInfo.gender = '男';
    } else if (symptoms.includes('患者女性') || symptoms.includes('女性')) {
        patientInfo.gender = '女';
    }

    // 提取年龄信息
    const ageMatch = symptoms.match(/(\d+)岁/);
    if (ageMatch) {
        patientInfo.age = ageMatch[1];
    }

    // 生成患者描述
    if (patientInfo.gender || patientInfo.age) {
        let desc = '患者';
        if (patientInfo.gender) {
            desc += patientInfo.gender + '性';
        }
        if (patientInfo.age) {
            desc += '，' + patientInfo.age + '岁';
        }
        patientInfo.description = desc;
    }

    return patientInfo;
}

// 提取脉象信息
function extractPulseInfo(symptoms) {
    const pulseInfo = {
        detected: [],
        analysis: ''
    };

    pulseTypes.forEach(pulse => {
        if (symptoms.includes(pulse.name) || symptoms.includes(`脉象${pulse.name}`)) {
            pulseInfo.detected.push(pulse);
        }
    });

    if (pulseInfo.detected.length > 0) {
        pulseInfo.analysis = `脉象：${pulseInfo.detected.map(p => p.name).join('、')}`;
    }

    return pulseInfo;
}

// 提取舌象信息
function extractTongueInfo(symptoms) {
    const tongueInfo = {
        quality: null,
        coating: null,
        analysis: ''
    };

    // 检查舌质
    tongueQualities.forEach(quality => {
        if (symptoms.includes(quality.name) || symptoms.includes(`舌质${quality.name}`)) {
            tongueInfo.quality = quality;
        }
    });

    // 检查舌苔
    tongueCoatings.forEach(coating => {
        if (symptoms.includes(coating.name) || symptoms.includes(`舌苔${coating.name}`)) {
            tongueInfo.coating = coating;
        }
    });

    // 生成分析
    const parts = [];
    if (tongueInfo.quality) {
        parts.push(`舌质${tongueInfo.quality.name}`);
    }
    if (tongueInfo.coating) {
        parts.push(`舌苔${tongueInfo.coating.name}`);
    }

    if (parts.length > 0) {
        tongueInfo.analysis = `舌象：${parts.join('，')}`;
    }

    return tongueInfo;
}

// 根据患者信息、脉象和舌象调整诊断
function adjustDiagnosisWithPatientInfo(baseDiagnosis, patientInfo, pulseInfo, tongueInfo) {
    let adjustedDiagnosis = baseDiagnosis;

    // 在诊断开头添加患者信息
    if (patientInfo.description) {
        adjustedDiagnosis = `**${patientInfo.description}**\n\n` + adjustedDiagnosis;
    }

    // 如果有脉象或舌象信息，添加到诊断中
    if (pulseInfo.analysis || tongueInfo.analysis) {
        const additionalInfo = [];
        if (pulseInfo.analysis) additionalInfo.push(pulseInfo.analysis);
        if (tongueInfo.analysis) additionalInfo.push(tongueInfo.analysis);

        // 在疾病机理部分添加脉象舌象分析
        const mechanismIndex = adjustedDiagnosis.indexOf('## 2. 疾病机理');
        if (mechanismIndex !== -1) {
            const nextSectionIndex = adjustedDiagnosis.indexOf('## 3.', mechanismIndex);
            const mechanismSection = adjustedDiagnosis.substring(mechanismIndex, nextSectionIndex);
            const restContent = adjustedDiagnosis.substring(nextSectionIndex);

            const enhancedMechanism = mechanismSection +
                `\n\n**四诊合参**：${additionalInfo.join('；')}，符合上述证候特点。\n`;

            adjustedDiagnosis = adjustedDiagnosis.substring(0, mechanismIndex) +
                enhancedMechanism + restContent;
        }
    }

    // 根据性别和年龄添加特殊建议
    if (patientInfo.gender === '女' && patientInfo.age) {
        const age = parseInt(patientInfo.age);
        if (age >= 45 && age <= 55) {
            // 更年期女性特殊建议
            adjustedDiagnosis += `\n\n**特殊提醒**：患者为更年期女性，需注意调理肝肾，疏肝解郁，可配合心理调节。`;
        } else if (age >= 18 && age <= 45) {
            // 育龄期女性特殊建议
            adjustedDiagnosis += `\n\n**特殊提醒**：患者为育龄期女性，需注意调理气血，关注月经周期变化。`;
        }
    }

    if (patientInfo.gender === '男' && patientInfo.age) {
        const age = parseInt(patientInfo.age);
        if (age >= 50) {
            // 中老年男性特殊建议
            adjustedDiagnosis += `\n\n**特殊提醒**：患者为中老年男性，需注意补肾壮阳，预防前列腺疾病。`;
        }
    }

    return adjustedDiagnosis;
}

// 保留原函数以兼容性
function adjustDiagnosisWithPulseTongue(baseDiagnosis, pulseInfo, tongueInfo) {
    return adjustDiagnosisWithPatientInfo(baseDiagnosis, {}, pulseInfo, tongueInfo);
}

// 保存诊断数据用于导出
function saveDiagnosisData(diagnosis) {
    const patientInfo = getPatientInfo();
    const symptoms = document.getElementById('symptoms').value.trim();

    currentDiagnosisData = {
        patientInfo: patientInfo,
        symptoms: symptoms,
        pulse: selectedPulse,
        tongueQuality: selectedTongueQuality,
        tongueCoating: selectedTongueCoating,
        diagnosis: diagnosis,
        timestamp: new Date().toLocaleString('zh-CN')
    };
}

// 导出诊断结果
function exportDiagnosis(format) {
    if (!currentDiagnosisData.diagnosis) {
        alert('请先获取诊断建议后再导出');
        return;
    }

    if (format === 'md') {
        exportToMarkdown();
    } else if (format === 'doc') {
        exportToWord();
    }
}

// 导出为Markdown格式
function exportToMarkdown() {
    const content = generateMarkdownContent();
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const filename = generateFilename('md');

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
}

// 导出为Word格式
function exportToWord() {
    try {
        // 检查docx库是否可用
        if (typeof docx === 'undefined') {
            throw new Error('docx库未加载');
        }

        const content = generateSimpleWordContent();
        const filename = generateFilename('docx');

        // 使用docx库创建Word文档
        const doc = new docx.Document({
            sections: [{
                properties: {},
                children: content
            }]
        });

        // 生成并下载文档
        docx.Packer.toBlob(doc).then(blob => {
            if (typeof saveAs !== 'undefined') {
                saveAs(blob, filename);
            } else {
                // 备用下载方法
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            }
        }).catch(error => {
            console.error('Word文档生成失败:', error);
            // 降级到HTML导出
            exportToHtml();
        });
    } catch (error) {
        console.error('Word导出失败:', error);
        // 降级到HTML导出
        exportToHtml();
    }
}

// 生成Markdown内容
function generateMarkdownContent() {
    const data = currentDiagnosisData;
    let content = `# 中医智慧诊疗系统 - 诊断报告\n\n`;

    // 基本信息
    content += `## 患者基本信息\n\n`;
    content += `- **生成时间**: ${data.timestamp}\n`;
    if (data.patientInfo.gender) {
        content += `- **性别**: ${data.patientInfo.gender}性\n`;
    }
    if (data.patientInfo.age) {
        content += `- **年龄**: ${data.patientInfo.age}岁\n`;
    }
    content += `\n`;

    // 症状信息
    content += `## 症状描述\n\n`;
    content += `${data.symptoms}\n\n`;

    // 四诊信息
    if (data.pulse || data.tongueQuality || data.tongueCoating) {
        content += `## 四诊信息\n\n`;
        if (data.pulse) {
            content += `- **脉象**: ${data.pulse}\n`;
        }
        if (data.tongueQuality) {
            content += `- **舌质**: ${data.tongueQuality}\n`;
        }
        if (data.tongueCoating) {
            content += `- **舌苔**: ${data.tongueCoating}\n`;
        }
        content += `\n`;
    }

    // 诊断结果
    content += `## 诊疗建议\n\n`;
    content += data.diagnosis;

    // 免责声明
    content += `\n\n---\n\n`;
    content += `**免责声明**: 本诊断建议仅供参考，不能替代专业医师诊断。请在专业中医师指导下进行治疗。\n\n`;
    content += `**生成系统**: 中医智慧诊疗系统\n`;
    content += `**生成时间**: ${data.timestamp}\n`;

    return content;
}

// 生成简化的Word文档内容
function generateSimpleWordContent() {
    const data = currentDiagnosisData;
    const children = [];

    try {
        // 标题
        children.push(
            new docx.Paragraph({
                text: "中医智慧诊疗系统 - 诊断报告",
                heading: docx.HeadingLevel.TITLE,
                alignment: docx.AlignmentType.CENTER
            })
        );

        // 基本信息
        children.push(
            new docx.Paragraph({
                text: "患者基本信息",
                heading: docx.HeadingLevel.HEADING_1
            })
        );

        children.push(
            new docx.Paragraph({
                text: `生成时间: ${data.timestamp}`
            })
        );

        if (data.patientInfo.gender) {
            children.push(
                new docx.Paragraph({
                    text: `性别: ${data.patientInfo.gender}性`
                })
            );
        }

        if (data.patientInfo.age) {
            children.push(
                new docx.Paragraph({
                    text: `年龄: ${data.patientInfo.age}岁`
                })
            );
        }

        // 症状描述
        children.push(
            new docx.Paragraph({
                text: "症状描述",
                heading: docx.HeadingLevel.HEADING_1
            })
        );

        children.push(
            new docx.Paragraph({
                text: data.symptoms || "无"
            })
        );

        // 诊疗建议
        children.push(
            new docx.Paragraph({
                text: "诊疗建议",
                heading: docx.HeadingLevel.HEADING_1
            })
        );

        // 简化诊断内容处理
        const diagnosisText = data.diagnosis.replace(/[#*]/g, '').replace(/\n+/g, '\n');
        const diagnosisLines = diagnosisText.split('\n').filter(line => line.trim());

        diagnosisLines.forEach(line => {
            if (line.trim()) {
                children.push(
                    new docx.Paragraph({
                        text: line.trim()
                    })
                );
            }
        });

        // 免责声明
        children.push(
            new docx.Paragraph({
                text: "免责声明: 本诊断建议仅供参考，不能替代专业医师诊断。"
            })
        );

    } catch (error) {
        console.error('Word内容生成错误:', error);
        // 返回基本内容
        return [
            new docx.Paragraph({
                text: "中医智慧诊疗系统 - 诊断报告"
            }),
            new docx.Paragraph({
                text: data.diagnosis || "诊断内容"
            })
        ];
    }

    return children;
}

// 生成Word文档内容（保留原函数以兼容）
function generateWordContent() {
    const data = currentDiagnosisData;
    const children = [];

    // 标题
    children.push(
        new docx.Paragraph({
            text: "中医智慧诊疗系统 - 诊断报告",
            heading: docx.HeadingLevel.TITLE,
            alignment: docx.AlignmentType.CENTER
        })
    );

    // 基本信息
    children.push(
        new docx.Paragraph({
            text: "患者基本信息",
            heading: docx.HeadingLevel.HEADING_1
        })
    );

    children.push(
        new docx.Paragraph({
            text: `生成时间: ${data.timestamp}`
        })
    );

    if (data.patientInfo.gender) {
        children.push(
            new docx.Paragraph({
                text: `性别: ${data.patientInfo.gender}性`
            })
        );
    }

    if (data.patientInfo.age) {
        children.push(
            new docx.Paragraph({
                text: `年龄: ${data.patientInfo.age}岁`
            })
        );
    }

    // 症状描述
    children.push(
        new docx.Paragraph({
            text: "症状描述",
            heading: docx.HeadingLevel.HEADING_1
        })
    );

    children.push(
        new docx.Paragraph({
            text: data.symptoms
        })
    );

    // 四诊信息
    if (data.pulse || data.tongueQuality || data.tongueCoating) {
        children.push(
            new docx.Paragraph({
                text: "四诊信息",
                heading: docx.HeadingLevel.HEADING_1
            })
        );

        if (data.pulse) {
            children.push(
                new docx.Paragraph({
                    text: `脉象: ${data.pulse}`
                })
            );
        }

        if (data.tongueQuality) {
            children.push(
                new docx.Paragraph({
                    text: `舌质: ${data.tongueQuality}`
                })
            );
        }

        if (data.tongueCoating) {
            children.push(
                new docx.Paragraph({
                    text: `舌苔: ${data.tongueCoating}`
                })
            );
        }
    }

    // 诊疗建议
    children.push(
        new docx.Paragraph({
            text: "诊疗建议",
            heading: docx.HeadingLevel.HEADING_1
        })
    );

    // 将诊断文本按行分割并添加
    const diagnosisLines = data.diagnosis.split('\n');
    diagnosisLines.forEach(line => {
        if (line.trim()) {
            children.push(
                new docx.Paragraph({
                    text: line.trim()
                })
            );
        }
    });

    // 免责声明
    children.push(
        new docx.Paragraph({
            text: "免责声明",
            heading: docx.HeadingLevel.HEADING_1
        })
    );

    children.push(
        new docx.Paragraph({
            text: "本诊断建议仅供参考，不能替代专业医师诊断。请在专业中医师指导下进行治疗。"
        })
    );

    children.push(
        new docx.Paragraph({
            text: `生成系统: 中医智慧诊疗系统`
        })
    );

    children.push(
        new docx.Paragraph({
            text: `生成时间: ${data.timestamp}`
        })
    );

    return children;
}

// 生成文件名
function generateFilename(extension) {
    const data = currentDiagnosisData;
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = date.toTimeString().slice(0, 8).replace(/:/g, '');

    let filename = `中医诊断报告_${dateStr}_${timeStr}`;

    if (data.patientInfo.gender || data.patientInfo.age) {
        filename += '_';
        if (data.patientInfo.gender) {
            filename += data.patientInfo.gender;
        }
        if (data.patientInfo.age) {
            filename += data.patientInfo.age + '岁';
        }
    }

    return `${filename}.${extension}`;
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // Ctrl+Enter 提交
    if (event.ctrlKey && event.key === 'Enter') {
        getDiagnosis();
    }

    // Ctrl+S 导出Markdown
    if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        if (currentDiagnosisData.diagnosis) {
            exportDiagnosis('md');
        }
    }

    // Ctrl+D 导出Word
    if (event.ctrlKey && event.key === 'd') {
        event.preventDefault();
        if (currentDiagnosisData.diagnosis) {
            exportDiagnosis('doc');
        }
    }
});

// HTML导出备用方案
function exportToHtml() {
    try {
        const content = generateHtmlContent();
        const blob = new Blob([content], { type: 'text/html;charset=utf-8' });
        const filename = generateFilename('html');

        // 创建下载链接
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);

        // 提示用户
        alert('Word导出暂时不可用，已为您导出HTML格式，可在浏览器中打开并打印为PDF');
    } catch (error) {
        console.error('HTML导出失败:', error);
        alert('导出功能暂时不可用，请稍后重试或使用Markdown导出');
    }
}

// 生成HTML内容
function generateHtmlContent() {
    const data = currentDiagnosisData;

    let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智慧诊疗系统 - 诊断报告</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .info-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .diagnosis-content {
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 15px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
        @media print {
            body { margin: 0; padding: 15px; }
        }
    </style>
</head>
<body>
    <h1>中医智慧诊疗系统 - 诊断报告</h1>

    <div class="info-section">
        <h2>患者基本信息</h2>
        <p><strong>生成时间：</strong>${data.timestamp}</p>`;

    if (data.patientInfo.gender) {
        html += `<p><strong>性别：</strong>${data.patientInfo.gender}性</p>`;
    }

    if (data.patientInfo.age) {
        html += `<p><strong>年龄：</strong>${data.patientInfo.age}岁</p>`;
    }

    html += `</div>

    <div class="info-section">
        <h2>症状描述</h2>
        <p>${data.symptoms || '无'}</p>
    </div>`;

    // 四诊信息
    if (data.pulse || data.tongueQuality || data.tongueCoating) {
        html += `<div class="info-section">
        <h2>四诊信息</h2>`;

        if (data.pulse) {
            html += `<p><strong>脉象：</strong>${data.pulse}</p>`;
        }
        if (data.tongueQuality) {
            html += `<p><strong>舌质：</strong>${data.tongueQuality}</p>`;
        }
        if (data.tongueCoating) {
            html += `<p><strong>舌苔：</strong>${data.tongueCoating}</p>`;
        }

        html += `</div>`;
    }

    html += `<div class="diagnosis-content">
        <h2>诊疗建议</h2>
        ${formatDiagnosisForHtml(data.diagnosis)}
    </div>

    <div class="footer">
        <p><strong>免责声明：</strong>本诊断建议仅供参考，不能替代专业医师诊断。请在专业中医师指导下进行治疗。</p>
        <p><strong>生成系统：</strong>中医智慧诊疗系统</p>
        <p><strong>生成时间：</strong>${data.timestamp}</p>
    </div>
</body>
</html>`;

    return html;
}

// 格式化诊断内容为HTML
function formatDiagnosisForHtml(diagnosis) {
    if (!diagnosis) return '';

    return diagnosis
        .replace(/^## (.*$)/gm, '<h3>$1</h3>')
        .replace(/^### (.*$)/gm, '<h4>$1</h4>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
        .replace(/^(.*)$/, '<p>$1</p>');
}
