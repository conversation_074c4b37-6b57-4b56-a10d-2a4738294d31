# 网络连接问题解决方案

## 🔧 问题诊断

您遇到的错误：
```
API调用失败: HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
```

这是典型的网络超时问题，可能的原因：
1. **网络连接不稳定**
2. **防火墙阻止连接**
3. **DNS解析问题**
4. **代理设置问题**
5. **DeepSeek服务器响应慢**

## ✅ 已实施的改进

我已经对系统进行了以下优化：

### 1. 增加超时时间
- 从30秒增加到60秒
- 给API更多响应时间

### 2. 添加重试机制
- 自动重试3次
- 递增等待时间（5秒、10秒、15秒）
- 区分不同类型的网络错误

### 3. 更好的错误处理
- 超时错误：提示检查网络连接
- 连接错误：提示检查网络设置
- 其他错误：通用重试机制

## 🚀 解决方案

### 方案一：重新测试（推荐）
系统已经优化，现在重新测试：

1. **刷新前端页面**
2. **输入症状**：如"头痛三天，伴有恶心"
3. **选择脉象舌象**
4. **点击获取诊断**
5. **等待结果**（可能需要1-2分钟）

### 方案二：检查网络连接
```bash
# 测试DNS解析
nslookup api.deepseek.com

# 测试连接
ping api.deepseek.com

# 测试HTTPS连接
curl -I https://api.deepseek.com
```

### 方案三：配置代理（如需要）
如果您使用代理，编辑 `backend/tcm_service.py`：

```python
# 在 _call_deepseek_api 方法中添加代理配置
proxies = {
    'http': 'http://your-proxy:port',
    'https': 'https://your-proxy:port'
}

response = requests.post(
    f'{self.base_url}/v1/chat/completions',
    headers=headers,
    json=data,
    timeout=60,
    proxies=proxies  # 添加这行
)
```

### 方案四：使用离线模式
如果网络问题持续，可以继续使用离线模式：
- 包含完整的脉象舌象功能
- 支持四诊合参分析
- 提供专业的中医诊断

## 🔍 网络测试工具

### 测试DeepSeek API连接
```bash
# Windows PowerShell
Test-NetConnection api.deepseek.com -Port 443

# 或使用curl测试
curl -v https://api.deepseek.com
```

### 检查防火墙设置
1. **Windows防火墙**：
   - 控制面板 → 系统和安全 → Windows Defender防火墙
   - 允许应用通过防火墙
   - 添加Python.exe

2. **企业防火墙**：
   - 联系网络管理员
   - 请求开放api.deepseek.com:443

## 📊 性能优化建议

### 1. 网络优化
- 使用稳定的网络连接
- 避免在网络高峰期使用
- 考虑使用有线连接替代WiFi

### 2. 系统优化
- 关闭不必要的网络应用
- 清理DNS缓存：`ipconfig /flushdns`
- 重启网络适配器

### 3. API使用优化
- 避免频繁调用API
- 合理使用症状描述长度
- 在网络稳定时进行测试

## 🎯 当前状态

✅ **后端服务**：运行正常，已优化网络处理
✅ **重试机制**：已启用，自动处理网络问题
✅ **超时设置**：已增加到60秒
✅ **错误处理**：已改进，提供详细错误信息

## 💡 使用建议

### 立即测试
1. **简单症状**：先测试简单症状，如"头痛"
2. **等待时间**：首次调用可能需要1-2分钟
3. **网络稳定**：确保网络连接稳定

### 复杂症状测试
成功后再测试复杂症状：
```
患者女性，28岁，月经不调3个月，经期延后7-10天，
经量偏少，色淡质稀，小腹隐痛，腰膝酸软，
畏寒肢冷，夜尿频多，舌质淡胖有齿痕，苔薄白，脉沉细
```

## 🔄 故障恢复

如果仍然遇到问题：

1. **重启后端服务**：
   ```bash
   cd backend
   python app.py
   ```

2. **检查API密钥**：
   - 确认密钥格式正确
   - 验证账号余额充足

3. **切换到离线模式**：
   - 系统会自动检测API不可用
   - 无缝切换到离线诊断

## 🎉 开始测试

**现在系统已经优化，请重新测试API功能！**

如果问题持续，离线模式仍然提供完整的中医诊疗体验。🏥✨
