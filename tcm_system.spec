# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent

block_cipher = None

a = Analysis(
    ['tcm_launcher.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=[
        # 包含后端Python文件
        ('backend/app.py', 'backend'),
        ('backend/demo_app.py', 'backend'),
        ('backend/tcm_service.py', 'backend'),
        ('backend/tcm_demo_service.py', 'backend'),
        ('backend/.env.example', 'backend'),

        # 包含前端文件
        ('frontend/index.html', 'frontend'),
        ('frontend/app.js', 'frontend'),

        # 包含文档文件
        ('README.md', '.'),
        ('USAGE_EXAMPLES.md', '.'),
        ('QUICK_START_DEEPSEEK.md', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask_cors',
        'requests',
        'python-dotenv',
        'openai',
        'tkinter',
        'threading',
        'webbrowser',
        'pathlib',
        'datetime',
        'typing',
        'json',
        'os',
        'sys',
        'time',
        'logging',
        'dotenv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='中医智慧诊疗系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TCM_System',
)
