# 中医智慧诊疗系统

基于DeepSeek API的智能中医诊断系统，提供专业的中医诊疗建议。

## 🚀 快速开始（无需安装任何软件！）

**🎉 离线模式 - 即开即用！**

1. **直接打开** 浏览器访问 `frontend/index.html`
2. **输入症状** 如"头痛三天"、"咳嗽有痰"、"胃痛胃胀"等
3. **获取诊断** 查看完整的中医诊疗建议
4. **无需后端** 系统自动使用内置知识库

✅ **支持症状**：头痛、咳嗽、胃痛等常见症状
✅ **完整功能**：诊断、方剂、针灸、注意事项
✅ **专业内容**：君臣佐使、经络穴位分析

## 功能特点

- 🏥 **智能诊断**：基于症状描述提供中医诊断
- 🔍 **病机分析**：深入分析疾病发生机理
- 💊 **方剂推荐**：提供具体的中药方剂和处方
- 🎯 **君臣佐使**：详细分析中药配伍原理
- 🔗 **针灸建议**：推荐相关经络穴位和针灸方法
- 👅 **脉象舌象**：支持14种脉象、9种舌质、10种舌苔选择
- 🔄 **四诊合参**：自动整合脉象舌象信息到诊断中
- 👤 **个性化诊断**：支持性别年龄选择，提供因人制宜的诊疗建议
- 🎯 **特殊建议**：根据患者性别年龄特点提供针对性建议
- 📋 **导出功能**：支持Markdown和Word格式导出诊疗建议
- ⌨️ **快捷操作**：支持键盘快捷键，提高使用效率
- ⚠️ **注意事项**：提供饮食调理和生活建议

## 系统架构

```
tcm-diagnosis-system/
├── backend/                 # Python Flask后端
│   ├── app.py              # Flask应用主文件
│   ├── tcm_service.py      # 中医诊疗服务
│   ├── requirements.txt    # Python依赖
│   └── .env.example        # 环境变量示例
├── frontend/               # HTML/JS前端
│   ├── index.html          # 主页面
│   └── app.js              # 前端逻辑
└── README.md               # 项目说明
```

## 版本说明

### 📦 演示版（推荐新手）
- **文件**: `demo_app.py` + `start_demo.bat`
- **特点**: 无需API配置，内置知识库，即开即用
- **支持**: 头痛、咳嗽、胃痛等常见症状
- **启动**: 双击 `start_demo.bat`

### 🚀 完整版（生产环境）
- **文件**: `app.py` + `start_system.bat`
- **特点**: 集成DeepSeek API，智能诊断，支持所有症状
- **配置**: 需要DeepSeek API密钥
- **启动**: 配置API后运行 `start_system.bat`

## 安装和运行

### 方式一：离线模式（推荐，无需安装）

1. **直接打开**：浏览器打开 `frontend/index.html`
2. **开始使用**：输入症状获取诊断
3. **自动切换**：系统检测到无后端时自动使用离线模式

### 方式二：完整版

1. **环境准备**：确保安装 Python 3.8+
2. **配置API**：
   ```bash
   cd backend
   cp .env.example .env
   # 编辑 .env 文件，添加DeepSeek API密钥
   ```
3. **启动服务**：双击 `start_system.bat`
4. **访问前端**：浏览器打开 `frontend/index.html`

### DeepSeek API配置

在 `backend/.env` 文件中设置：
```
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

## 使用说明

1. **填写患者信息**：选择性别（男性/女性），输入年龄
2. **选择症状**：点击常见症状按钮快速添加
3. **选择脉象**：点击脉象按钮（如浮脉、沉脉等）
4. **选择舌象**：分别选择舌质和舌苔
5. **输入详情**：在症状描述框中补充详细信息
6. **获取诊断**：点击"获取中医诊断建议"按钮
7. **查看结果**：系统将返回个性化的完整诊疗建议

### 🆕 个性化诊断功能
- **性别选择**：男性/女性，影响诊断和治疗建议
- **年龄输入**：1-120岁，提供年龄相关的特殊建议
- **智能整合**：自动将患者信息整合到诊断开头

### 🆕 脉象舌象功能
- **14种脉象**：浮、沉、迟、数、滑、涩、弦、紧、缓、洪、细、弱、虚、实
- **9种舌质**：淡红、淡白、红、绛红、紫暗、胖大、瘦薄、有齿痕、有瘀斑
- **10种舌苔**：薄白、厚白、薄黄、厚黄、白腻、黄腻、少苔、无苔、剥苔、黑苔
- **四诊合参**：自动将脉象舌象信息整合到"疾病机理"部分

### 🆕 导出功能
- **Markdown导出**：📄 导出MD格式，轻量级文本格式
- **Word导出**：📝 导出DOC格式，专业文档格式
- **智能命名**：自动生成包含时间和患者信息的文件名
- **完整信息**：包含患者信息、症状、四诊、诊疗建议
- **快捷键**：Ctrl+S (MD导出)、Ctrl+D (DOC导出)

## API接口

### 获取诊断建议
```
POST /api/diagnosis
Content-Type: application/json

{
  "symptoms": "头痛三天，伴有恶心，舌苔厚腻"
}
```

### 获取常见症状
```
GET /api/common-symptoms
```

## 技术栈

- **后端**：Python Flask + DeepSeek API
- **前端**：HTML5 + JavaScript + Tailwind CSS
- **AI模型**：DeepSeek Chat API

## 注意事项

⚠️ **重要提醒**：
- 本系统仅提供中医理论参考，不能替代专业医师诊断
- 所有治疗建议仅供参考，请在专业中医师指导下进行治疗
- 如有严重症状，请及时就医

## 开发计划

- [ ] 添加用户认证系统
- [ ] 实现诊断历史记录
- [ ] 增加中药材数据库
- [ ] 添加症状图片识别
- [ ] 支持多语言界面

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
