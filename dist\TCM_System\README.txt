中医智慧诊疗系统 v2.1 - 独立运行版
=====================================

🎉 恭喜！您已获得完全独立运行的中医智慧诊疗系统！

📦 包含内容
----------
📁 TCM_System/
├── 🚀 TCM_System.exe         # 主程序（双击运行）
├── 📄 使用说明.txt           # 详细使用说明
├── 📄 README.txt             # 本文件
└── 📁 _internal/             # 程序依赖文件
    ├── 📁 backend/           # 后端服务文件
    ├── 📁 frontend/          # 前端界面文件
    └── 📁 其他依赖...        # Python运行时和库文件

✨ 主要特点
----------
• ✅ 完全独立运行，无需安装Python
• ✅ 双版本支持：演示版 + 完整版
• ✅ 图形化启动界面，操作简单
• ✅ 现代化Web前端，体验流畅
• ✅ 专业中医诊断功能
• ✅ 脉象舌象四诊合参
• ✅ 个性化诊疗建议
• ✅ 导出功能支持

🚀 立即开始
----------
1. 双击 "TCM_System.exe" 启动程序
2. 选择版本：
   - 演示版：即开即用，内置知识库
   - 完整版：需要配置DeepSeek API密钥
3. 点击"打开前端界面"
4. 开始您的中医诊疗之旅！

📋 系统要求
----------
• 操作系统：Windows 10/11 (64位)
• 内存：至少 2GB RAM
• 磁盘空间：约 200MB
• 网络：完整版需要网络连接

🔧 配置说明
----------
演示版：
• 无需任何配置
• 支持头痛、咳嗽、胃痛等常见症状
• 内置专业中医知识库

完整版：
• 需要DeepSeek API密钥
• 支持所有症状类型
• AI智能诊断分析
• 获取API密钥：https://platform.deepseek.com/

💡 使用建议
----------
1. 首次使用建议先体验演示版
2. 详细描述症状以获得更准确的诊断
3. 结合脉象舌象选择提高诊断质量
4. 完整版支持复杂症状组合分析

⚠️ 重要提醒
----------
• 本系统仅供学习和参考使用
• 不能替代专业医师诊断
• 如有严重症状请及时就医
• 所有治疗建议需在专业指导下执行

📞 技术支持
----------
• 详细说明：查看"使用说明.txt"
• 常见问题：程序内置故障排除指南
• 在线支持：访问项目官网

🎯 版本信息
----------
版本：v2.1 (修复版)
更新：修复Flask模块导入问题
大小：约150MB
兼容：Windows 10/11

🎉 开始体验
----------
双击 "TCM_System.exe" 开始您的中医智慧诊疗体验！

祝您使用愉快！ 🏥✨
