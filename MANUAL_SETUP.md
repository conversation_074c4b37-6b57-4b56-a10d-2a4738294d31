# 手动配置DeepSeek API完整版

## 🎯 目标
手动配置并运行完整版中医智慧诊疗系统，调用DeepSeek API。

## 📋 前置条件
- ✅ Python 3.10 已安装
- ⚠️ 需要配置DeepSeek API密钥

## 🔧 配置步骤

### 第一步：验证Python环境

打开命令提示符（cmd），尝试以下命令：

```bash
# 方法1
python --version

# 方法2
py --version

# 方法3
python3 --version
```

如果任何一个命令显示 `Python 3.10.x`，说明Python可用。

**如果都不工作**：
1. 重新安装Python，勾选"Add Python to PATH"
2. 或者找到Python安装目录，手动添加到PATH环境变量

### 第二步：获取DeepSeek API密钥

1. **注册DeepSeek账号**
   - 访问：https://platform.deepseek.com/
   - 注册并完成邮箱验证

2. **获取API密钥**
   - 登录后进入控制台
   - 找到"API密钥"或"API Keys"页面
   - 点击"创建新密钥"
   - 复制生成的密钥（格式：sk-xxxxxxxxxxxxxxxx）

### 第三步：配置API密钥

编辑文件 `backend/.env`，将内容修改为：

```
DEEPSEEK_API_KEY=sk-your-actual-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

**重要**：将 `sk-your-actual-api-key-here` 替换为您的真实API密钥。

### 第四步：安装Python依赖

打开命令提示符，执行：

```bash
# 进入项目目录
cd "C:\Mac\Home\Documents\win py\backend"

# 安装依赖（使用您可用的Python命令）
python -m pip install flask flask-cors requests python-dotenv openai

# 如果上面不工作，尝试：
py -m pip install flask flask-cors requests python-dotenv openai

# 或者：
python3 -m pip install flask flask-cors requests python-dotenv openai
```

**如果安装失败**，尝试使用国内镜像：

```bash
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors requests python-dotenv openai
```

### 第五步：启动后端服务

在 `backend` 目录下执行：

```bash
# 使用您可用的Python命令
python app.py

# 或者：
py app.py

# 或者：
python3 app.py
```

**成功启动**会显示：
```
* Running on http://127.0.0.1:5000
* Debug mode: on
```

### 第六步：访问前端

1. 保持后端服务运行
2. 打开浏览器
3. 访问 `frontend/index.html` 文件

## ✅ 验证配置

### 测试后端API
在浏览器中访问：http://localhost:5000/

应该看到：
```json
{
  "status": "healthy",
  "message": "中医智慧诊疗系统后端服务运行正常",
  "service_available": true
}
```

### 测试完整功能
1. 在前端页面输入复杂症状
2. 选择脉象和舌象
3. 点击"获取中医诊断建议"
4. 查看DeepSeek AI的专业分析

## 🔧 故障排除

### 问题1：Python命令不工作
**解决方案**：
1. 重新安装Python，确保勾选"Add Python to PATH"
2. 重启命令提示符
3. 或者使用完整路径运行Python

### 问题2：pip安装失败
**解决方案**：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple [包名]
```

### 问题3：API调用失败
**检查**：
1. API密钥是否正确
2. 网络连接是否正常
3. DeepSeek账号是否有余额

### 问题4：端口被占用
**解决方案**：
1. 关闭占用5000端口的程序
2. 或者修改 `app.py` 中的端口号

## 🚀 快速启动命令

创建一个 `start.bat` 文件，内容：

```batch
@echo off
cd /d "C:\Mac\Home\Documents\win py\backend"
python app.py
pause
```

双击运行即可启动服务。

## 💡 使用建议

### 测试复杂症状
```
患者男性，45岁，胸痛反复发作1月余，痛如针刺，
固定不移，夜间加重，伴心悸气短，面色晦暗，
唇甲青紫，舌质紫暗有瘀斑，脉象涩而无力
```

### 选择对应脉象舌象
- 脉象：涩脉
- 舌质：紫暗、有瘀斑

### 查看AI分析
DeepSeek会提供专业的中医分析，包括：
- 详细的病机分析
- 个性化的治疗方案
- 精准的方药配伍
- 针灸穴位建议

## 🎉 完成

配置完成后，您就可以体验AI驱动的专业中医诊疗系统了！

**享受智能中医诊断的强大功能吧！** 🏥🤖✨
