# Tcl autoload index file, version 2.0
# This file is generated by the "auto_mkindex" command
# and sourced to set up indexing information for one or
# more commands.  Typically each line is a command that
# sets an element in the auto_index array, where the
# element name is the name of a command and the value is
# a script that loads the command.

set auto_index(auto_reset) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(tcl_findLibrary) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(auto_mkindex) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(auto_mkindex_old) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::init) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::cleanup) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::mkindex) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::hook) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::slavehook) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::command) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::commandInit) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::auto_mkindex_parser::fullname) [list source -encoding utf-8 [file join $dir auto.tcl]]
set auto_index(::tcl::history) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(history) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistAdd) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistKeep) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistClear) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistInfo) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistRedo) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistIndex) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistEvent) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistChange) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::HistNextID) [list source -encoding utf-8 [file join $dir history.tcl]]
set auto_index(::tcl::Pkg::CompareExtension) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(pkg_mkIndex) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(tclPkgSetup) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(tclPkgUnknown) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(::tcl::MacOSXPkgUnknown) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(::pkg::create) [list source -encoding utf-8 [file join $dir package.tcl]]
set auto_index(parray) [list source -encoding utf-8 [file join $dir parray.tcl]]
set auto_index(::safe::InterpStatics) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::InterpNested) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpCreate) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpInit) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::CheckInterp) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpConfigure) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::InterpCreate) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::InterpSetConfig) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpFindInAccessPath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpAddToAccessPath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::InterpInit) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AddSubDirs) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::interpDelete) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::setLogCmd) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::SyncAccessPath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::PathToken) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::TranslatePath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::Log) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::CheckFileName) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasFileSubcommand) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasGlob) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasSource) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasLoad) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::FileInAccessPath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::DirInAccessPath) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::BadSubcommand) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasEncoding) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::AliasExeName) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::RejectExcessColons) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::VarName) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::safe::Setup) [list source -encoding utf-8 [file join $dir safe.tcl]]
set auto_index(::tcl::tm::path) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::add) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::remove) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::list) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::UnknownHandler) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::Defaults) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::tm::roots) [list source -encoding utf-8 [file join $dir tm.tcl]]
set auto_index(::tcl::UpdateWordBreakREs) [list source -encoding utf-8 [file join $dir word.tcl]]
set auto_index(tcl_wordBreakAfter) [list source -encoding utf-8 [file join $dir word.tcl]]
set auto_index(tcl_wordBreakBefore) [list source -encoding utf-8 [file join $dir word.tcl]]
set auto_index(tcl_endOfWord) [list source -encoding utf-8 [file join $dir word.tcl]]
set auto_index(tcl_startOfNextWord) [list source -encoding utf-8 [file join $dir word.tcl]]
set auto_index(tcl_startOfPreviousWord) [list source -encoding utf-8 [file join $dir word.tcl]]
if {[namespace exists ::tcl::unsupported]} {
    set auto_index(timerate) {namespace import ::tcl::unsupported::timerate}
}
