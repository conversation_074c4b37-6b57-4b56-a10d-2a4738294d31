# 🚀 中医智慧诊疗系统 - 快速演示

## 🎯 立即体验（30秒开始使用）

### 第一步：打开系统
- 双击打开 `frontend/index.html` 文件
- 或者将文件拖拽到浏览器中

### 第二步：选择症状
点击常见症状按钮快速添加：
- 头痛、头晕
- 咳嗽、咳痰  
- 胃痛、腹胀

### 第三步：获取诊断
- 点击"获取中医诊断建议"按钮
- 等待1-2秒加载
- 查看完整的诊疗建议

## 📝 演示示例

### 示例1：头痛症状
**输入**：`头痛三天，伴有恶心`

**输出**：
- 🔍 **诊断**：风寒头痛
- 🧬 **机理**：风寒外袭，经络阻滞
- 💊 **方剂**：川芎茶调散加减
- 🎯 **配伍**：君臣佐使详细分析
- 🔗 **针灸**：百会、风池、太阳、合谷
- ⚠️ **注意**：饮食起居指导

### 示例2：咳嗽症状
**输入**：`咳嗽一周，有白痰`

**输出**：
- 🔍 **诊断**：风寒咳嗽
- 🧬 **机理**：风寒犯肺，肺气失宣
- 💊 **方剂**：三拗汤加减
- 🎯 **配伍**：麻黄、杏仁、桔梗等
- 🔗 **针灸**：肺俞、列缺、尺泽
- ⚠️ **注意**：保暖避寒指导

### 示例3：胃痛症状
**输入**：`胃痛隐隐，喜温喜按`

**输出**：
- 🔍 **诊断**：脾胃虚寒
- 🧬 **机理**：脾胃阳虚，寒从中生
- 💊 **方剂**：理中汤加减
- 🎯 **配伍**：干姜、白术、人参等
- 🔗 **针灸**：中脘、足三里、脾俞
- ⚠️ **注意**：温热饮食指导

## 🌟 系统特色

### 专业性
- ✅ 基于传统中医理论
- ✅ 完整的辨证论治流程
- ✅ 详细的君臣佐使分析
- ✅ 专业的针灸穴位建议

### 易用性
- ✅ 无需安装任何软件
- ✅ 一键快速选择症状
- ✅ 实时加载状态提示
- ✅ 移动端友好界面

### 安全性
- ✅ 完全离线运行
- ✅ 无需网络连接
- ✅ 不收集用户数据
- ✅ 明确免责声明

## 🔧 技术亮点

### 智能切换
- 自动检测后端服务状态
- 无缝切换到离线模式
- 保持完整用户体验

### 响应式设计
- 支持桌面和移动设备
- 现代化渐变界面
- 平滑动画效果

### 错误处理
- 友好的错误提示
- 自动重试机制
- 降级方案支持

## 📚 学习价值

### 中医知识
- 学习传统中医诊断思路
- 了解中药配伍原理
- 掌握针灸穴位应用

### 技术实现
- 前后端分离架构
- API设计最佳实践
- 离线优先策略

## 🎓 适用人群

### 中医爱好者
- 学习中医基础理论
- 了解常见病症治疗
- 体验传统医学魅力

### 技术开发者
- 参考系统架构设计
- 学习前端交互实现
- 了解AI集成方案

### 医学学生
- 辅助中医学习
- 理解诊疗流程
- 巩固理论知识

## ⚠️ 重要提醒

1. **仅供参考**：本系统提供的建议仅供学习参考
2. **专业就诊**：如有疾病请及时就诊专业中医师
3. **个体差异**：中医讲究辨证论治，需结合个人体质
4. **安全用药**：所有用药建议需在医师指导下执行

## 🚀 下一步

体验完离线模式后，您可以：
1. 查看 `USAGE_EXAMPLES.md` 了解更多使用示例
2. 阅读 `README.md` 获取完整功能说明
3. 安装Python环境使用完整版（集成AI功能）
4. 基于现有代码进行二次开发

**开始您的中医智慧诊疗之旅吧！** 🏥✨
