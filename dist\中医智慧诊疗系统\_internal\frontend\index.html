<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智慧诊疗系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 用于Word文档导出的库 -->
    <script src="https://cdn.jsdelivr.net/npm/docx@7.8.2/build/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .loading {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center">🏥 中医智慧诊疗系统</h1>
            <p class="text-center mt-2 opacity-90">基于AI的传统中医诊断与治疗建议</p>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 输入区域 -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg card-shadow p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">📝 患者信息与症状描述</h2>

                <!-- 患者基本信息 -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-600 mb-3">患者基本信息：</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- 性别选择 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">性别：</label>
                            <div class="flex gap-3">
                                <label class="flex items-center">
                                    <input type="radio" name="gender" value="男" class="mr-2 text-blue-600">
                                    <span class="text-sm">男性</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="gender" value="女" class="mr-2 text-blue-600">
                                    <span class="text-sm">女性</span>
                                </label>
                            </div>
                        </div>

                        <!-- 年龄选择 -->
                        <div>
                            <label for="age" class="block text-sm font-medium text-gray-700 mb-2">年龄：</label>
                            <div class="flex items-center gap-2">
                                <input
                                    type="number"
                                    id="age"
                                    min="1"
                                    max="120"
                                    placeholder="请输入年龄"
                                    class="w-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                >
                                <span class="text-sm text-gray-600">岁</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 常见症状快速选择 -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-600 mb-2">常见症状（点击快速添加）：</h3>
                    <div class="flex flex-wrap gap-2" id="commonSymptoms">
                        <!-- 动态加载常见症状 -->
                    </div>
                </div>

                <!-- 脉象选择 -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-600 mb-2">脉象（可选）：</h3>
                    <div class="flex flex-wrap gap-2" id="pulseOptions">
                        <!-- 动态加载脉象选项 -->
                    </div>
                </div>

                <!-- 舌象选择 -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-600 mb-2">舌象（可选）：</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                            <h4 class="text-xs font-medium text-gray-500 mb-1">舌质：</h4>
                            <div class="flex flex-wrap gap-1" id="tongueQualityOptions">
                                <!-- 动态加载舌质选项 -->
                            </div>
                        </div>
                        <div>
                            <h4 class="text-xs font-medium text-gray-500 mb-1">舌苔：</h4>
                            <div class="flex flex-wrap gap-1" id="tongueCoatingOptions">
                                <!-- 动态加载舌苔选项 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 症状输入框 -->
                <div class="mb-4">
                    <label for="symptoms" class="block text-sm font-medium text-gray-700 mb-2">
                        请详细描述您的症状：
                    </label>
                    <textarea
                        id="symptoms"
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="请描述您的症状，如：头痛三天，伴有恶心，舌苔厚腻，脉象滑数等..."
                    ></textarea>
                </div>

                <!-- 提交按钮 -->
                <button
                    id="submitBtn"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
                    onclick="getDiagnosis()"
                >
                    <span id="submitText">获取中医诊断建议</span>
                    <div id="loadingSpinner" class="loading mx-auto hidden"></div>
                </button>
            </div>

            <!-- 结果显示区域 -->
            <div id="resultArea" class="hidden">
                <div class="bg-white rounded-lg card-shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">🔍 诊疗建议</h2>
                        <!-- 导出按钮组 -->
                        <div class="flex gap-2">
                            <button
                                id="exportMdBtn"
                                class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition duration-200"
                                onclick="exportDiagnosis('md')"
                                title="导出为Markdown格式"
                            >
                                📄 导出MD
                            </button>
                            <button
                                id="exportDocBtn"
                                class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
                                onclick="exportDiagnosis('doc')"
                                title="导出为Word格式"
                            >
                                📝 导出DOC
                            </button>
                        </div>
                    </div>
                    <div id="diagnosisResult" class="prose max-w-none">
                        <!-- 诊断结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 错误提示区域 -->
            <div id="errorArea" class="hidden">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span class="text-red-400">⚠️</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">出现错误</h3>
                            <div class="mt-2 text-sm text-red-700" id="errorMessage">
                                <!-- 错误信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 免责声明 -->
    <footer class="bg-gray-100 py-6 mt-12">
        <div class="container mx-auto px-4 text-center text-gray-600">
            <p class="text-sm">
                ⚠️ <strong>免责声明：</strong>本系统仅提供中医理论参考，不能替代专业医师诊断。
                请在专业中医师指导下进行治疗。
            </p>
        </div>
    </footer>

    <script src="app.js"></script>
</body>
</html>
