('F:\\win py\\win py\\build\\中医智慧诊疗系统\\PYZ-00.pyz',
 [('__future__', 'D:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\Python312\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\Python312\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\Python312\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\Python312\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\Python312\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\Python312\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\Python312\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\Python312\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\Python312\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\Python312\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\Python312\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\Python312\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\Python312\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\Python312\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\Python312\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Python312\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Python312\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent', 'D:\\Python312\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'D:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('idna', 'D:\\Python312\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\Python312\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\Python312\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Python312\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Python312\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Python312\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'D:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\Python312\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Python312\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Python312\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Python312\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Python312\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Python312\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Python312\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Python312\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Python312\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Python312\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Python312\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Python312\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Python312\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Python312\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Python312\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Python312\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Python312\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('ssl', 'D:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\Python312\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Python312\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog', 'D:\\Python312\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Python312\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Python312\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Python312\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\Python312\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Python312\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Python312\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\Python312\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\Python312\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\Python312\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\Python312\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\Python312\\Lib\\zipimport.py', 'PYMODULE')])
