@echo off
chcp 65001 >nul
echo ========================================
echo 中医智慧诊疗系统 - 打包脚本
echo ========================================

echo.
echo 第一步：检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo 第二步：检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller已就绪

echo.
echo 第三步：检查依赖包...
echo 正在安装/更新依赖包...
pip install flask flask-cors requests python-dotenv openai >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 部分依赖包安装失败，尝试继续...
)
echo ✅ 依赖包检查完成

echo.
echo 第四步：清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "backend\__pycache__" rmdir /s /q "backend\__pycache__"
echo ✅ 清理完成

echo.
echo 第五步：开始打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller tcm_system.spec --clean --noconfirm

if %errorlevel% neq 0 (
    echo.
    echo ❌ 打包失败！
    echo.
    echo 可能的解决方案：
    echo 1. 检查Python版本是否为3.8+
    echo 2. 确保所有依赖包已正确安装
    echo 3. 检查防火墙或杀毒软件是否阻止了打包过程
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 打包完成！
echo ========================================
echo.
echo 📁 打包文件位置: dist\TCM_System\
echo 📄 主程序: 中医智慧诊疗系统.exe
echo.
echo 💡 使用说明:
echo 1. 将整个 TCM_System 文件夹复制到目标电脑
echo 2. 双击运行 "中医智慧诊疗系统.exe"
echo 3. 选择演示版或完整版启动
echo 4. 完整版需要配置DeepSeek API密钥
echo.
echo 📦 文件夹大小: 
for /f %%i in ('powershell -command "(Get-ChildItem -Path 'dist\TCM_System' -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB"') do echo 约 %%i MB
echo.

REM 创建使用说明文件
echo 创建使用说明文件...
(
echo 中医智慧诊疗系统 - 使用说明
echo ================================
echo.
echo 🚀 快速开始:
echo 1. 双击运行 "中医智慧诊疗系统.exe"
echo 2. 选择要启动的版本:
echo    - 演示版: 无需配置，即开即用
echo    - 完整版: 需要DeepSeek API密钥
echo.
echo 📋 版本对比:
echo ┌─────────────┬──────────┬──────────┐
echo │    功能     │  演示版  │  完整版  │
echo ├─────────────┼──────────┼──────────┤
echo │ 基础诊断    │    ✅    │    ✅    │
echo │ 脉象舌象    │    ✅    │    ✅    │
echo │ 支持症状    │ 常见症状 │ 所有症状 │
echo │ AI智能分析  │    ❌    │    ✅    │
echo │ 个性化诊断  │    ❌    │    ✅    │
echo └─────────────┴──────────┴──────────┘
echo.
echo ⚙️ 完整版配置:
echo 1. 访问 https://platform.deepseek.com/
echo 2. 注册账号并获取API密钥
echo 3. 在程序中点击"配置API"按钮
echo 4. 输入API密钥并保存
echo.
echo 🌐 前端界面:
echo 程序启动后，点击"打开前端界面"按钮
echo 或直接在浏览器中打开 frontend\index.html
echo.
echo 📞 技术支持:
echo 如遇问题，请查看项目文档或联系开发者
echo.
echo 版本: v2.0 ^| 打包时间: %date% %time%
) > "dist\TCM_System\使用说明.txt"

echo ✅ 使用说明文件已创建

echo.
echo 🎯 下一步:
echo 1. 测试打包后的程序是否正常运行
echo 2. 将 dist\TCM_System 文件夹分发给用户
echo 3. 用户无需安装Python即可直接运行
echo.

REM 询问是否立即测试
set /p test_choice="是否立即测试打包后的程序? (y/n): "
if /i "%test_choice%"=="y" (
    echo.
    echo 🧪 启动测试...
    cd "dist\TCM_System"
    start "中医智慧诊疗系统.exe"
    cd ..\..
)

echo.
echo 打包完成！按任意键退出...
pause >nul
