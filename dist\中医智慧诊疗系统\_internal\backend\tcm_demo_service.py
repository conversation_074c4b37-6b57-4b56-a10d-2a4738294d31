"""
中医诊疗演示服务 - 不依赖外部API的演示版本
"""
from typing import Dict, Any
from datetime import datetime

class TCMDemoService:
    def __init__(self):
        # 预设的中医诊疗知识库
        self.tcm_knowledge = {
            "头痛": {
                "diagnosis": "风寒头痛",
                "mechanism": "风寒外袭，经络阻滞，清阳不升",
                "treatment": "疏风散寒，通络止痛",
                "formula": "川芎茶调散加减",
                "prescription": {
                    "君药": "川芎12g - 活血行气，祛风止痛",
                    "臣药": "白芷9g - 祛风解表，通窍止痛；荆芥9g - 祛风解表",
                    "佐药": "防风6g - 祛风胜湿；薄荷6g - 疏散风热",
                    "使药": "甘草3g - 调和诸药；茶叶3g - 清头目"
                },
                "acupuncture": {
                    "经络": "督脉、足太阳膀胱经",
                    "穴位": "百会、风池、太阳、合谷",
                    "方法": "平补平泻，留针20-30分钟"
                },
                "precautions": {
                    "饮食": "忌食生冷、辛辣刺激性食物",
                    "起居": "注意头部保暖，避免风寒",
                    "禁忌": "孕妇慎用",
                    "复诊": "3-5天后复诊"
                }
            },
            "咳嗽": {
                "diagnosis": "风寒咳嗽",
                "mechanism": "风寒犯肺，肺气失宣，津液凝聚成痰",
                "treatment": "疏风散寒，宣肺止咳",
                "formula": "三拗汤加减",
                "prescription": {
                    "君药": "麻黄6g - 宣肺平喘，发汗解表",
                    "臣药": "杏仁9g - 降气止咳；桔梗9g - 宣肺祛痰",
                    "佐药": "紫苏叶9g - 理气宽中；陈皮6g - 理气化痰",
                    "使药": "甘草3g - 调和诸药，润肺止咳"
                },
                "acupuncture": {
                    "经络": "手太阴肺经、督脉",
                    "穴位": "肺俞、列缺、尺泽、天突",
                    "方法": "补法为主，留针25分钟"
                },
                "precautions": {
                    "饮食": "忌食寒凉、油腻食物，多饮温水",
                    "起居": "注意保暖，避免受凉",
                    "禁忌": "高血压患者慎用麻黄",
                    "复诊": "一周后复诊"
                }
            },
            "胃痛": {
                "diagnosis": "脾胃虚寒",
                "mechanism": "脾胃阳虚，寒从中生，胃失温煦",
                "treatment": "温中健脾，和胃止痛",
                "formula": "理中汤加减",
                "prescription": {
                    "君药": "干姜9g - 温中散寒",
                    "臣药": "白术12g - 健脾燥湿；人参9g - 大补元气",
                    "佐药": "陈皮6g - 理气和胃；木香6g - 行气止痛",
                    "使药": "甘草6g - 调和诸药，缓急止痛"
                },
                "acupuncture": {
                    "经络": "足阳明胃经、足太阴脾经",
                    "穴位": "中脘、足三里、脾俞、胃俞",
                    "方法": "温针灸，留针30分钟"
                },
                "precautions": {
                    "饮食": "温热易消化食物，少食多餐",
                    "起居": "腹部保暖，规律作息",
                    "禁忌": "忌食生冷、辛辣食物",
                    "复诊": "两周后复诊"
                }
            }
        }
    
    def get_tcm_diagnosis(self, symptoms: str) -> Dict[str, Any]:
        """
        根据症状获取中医诊断建议
        """
        try:
            # 简单的关键词匹配
            diagnosis_result = self._match_symptoms(symptoms)
            
            if diagnosis_result:
                formatted_result = self._format_diagnosis(diagnosis_result)
                return {
                    "success": True,
                    "diagnosis": formatted_result,
                    "timestamp": self._get_current_time()
                }
            else:
                # 返回通用建议
                return {
                    "success": True,
                    "diagnosis": self._get_general_advice(symptoms),
                    "timestamp": self._get_current_time()
                }
                
        except Exception as e:
            return {
                "error": f"诊断分析失败: {str(e)}",
                "success": False
            }
    
    def _match_symptoms(self, symptoms: str) -> Dict[str, Any]:
        """
        匹配症状关键词
        """
        symptoms_lower = symptoms.lower()
        
        # 关键词匹配
        if any(keyword in symptoms for keyword in ["头痛", "头疼", "头晕"]):
            return self.tcm_knowledge["头痛"]
        elif any(keyword in symptoms for keyword in ["咳嗽", "咳痰", "咳喘"]):
            return self.tcm_knowledge["咳嗽"]
        elif any(keyword in symptoms for keyword in ["胃痛", "胃胀", "腹痛", "消化不良"]):
            return self.tcm_knowledge["胃痛"]
        
        return None
    
    def _format_diagnosis(self, diagnosis_data: Dict[str, Any]) -> str:
        """
        格式化诊断结果
        """
        result = f"""## 1. 中医诊断
{diagnosis_data['diagnosis']}

## 2. 疾病机理
{diagnosis_data['mechanism']}

## 3. 治疗方案
{diagnosis_data['treatment']}

## 4. 推荐方剂
{diagnosis_data['formula']}

## 5. 中药处方分析
### 君药：{diagnosis_data['prescription']['君药']}
### 臣药：{diagnosis_data['prescription']['臣药']}
### 佐药：{diagnosis_data['prescription']['佐药']}
### 使药：{diagnosis_data['prescription']['使药']}

## 6. 针灸治疗
### 主要经络：{diagnosis_data['acupuncture']['经络']}
### 推荐穴位：{diagnosis_data['acupuncture']['穴位']}
### 针灸方法：{diagnosis_data['acupuncture']['方法']}

## 7. 注意事项
### 饮食调理：{diagnosis_data['precautions']['饮食']}
### 生活起居：{diagnosis_data['precautions']['起居']}
### 禁忌事项：{diagnosis_data['precautions']['禁忌']}
### 复诊建议：{diagnosis_data['precautions']['复诊']}

**重要提醒：以上建议仅供参考，请在专业中医师指导下进行治疗。**"""
        
        return result
    
    def _get_general_advice(self, symptoms: str) -> str:
        """
        获取通用建议
        """
        return f"""## 中医诊疗建议

### 症状描述
您描述的症状：{symptoms}

### 一般性建议
根据中医理论，建议您：

1. **辨证论治**：中医讲究个体化治疗，需要结合您的体质、舌象、脉象等进行综合分析
2. **调理脾胃**：脾胃为后天之本，建议规律饮食，避免生冷
3. **情志调节**：保持心情舒畅，避免过度焦虑
4. **适度运动**：根据体质选择合适的运动方式
5. **作息规律**：早睡早起，顺应自然规律

### 建议就诊
由于症状的复杂性，建议您：
- 及时就诊专业中医师
- 进行详细的中医四诊（望、闻、问、切）
- 根据具体情况制定个性化治疗方案

**免责声明：本系统仅提供中医理论参考，不能替代专业医师诊断。**"""
    
    def _get_current_time(self) -> str:
        """
        获取当前时间
        """
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
